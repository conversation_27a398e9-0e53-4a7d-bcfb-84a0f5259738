import { createRouter, createWebHistory } from 'vue-router'
import Store from '../components/Store.vue'
import Admin from '../components/Admin.vue'
import AdminItemEditor from '../components/AdminItemEditor.vue'
import AdminCategories from '../components/AdminCategories.vue'

const routes = [
  {
    path: '/',
    name: 'Store',
    component: Store
  },
  {
    path: '/admin',
    name: 'Admin',
    component: Admin
  },
  {
    path: '/admin/items',
    name: 'AdminItems',
    component: AdminItemEditor
  },
  {
    path: '/admin/categories',
    name: 'AdminCategories',
    component: AdminCategories
  },
  {
    path: '/new',
    name: 'New',
    component: Admin
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
