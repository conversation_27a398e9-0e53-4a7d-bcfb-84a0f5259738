import { defineStore } from 'pinia'
import axios from 'axios'

const API = (import.meta as any).env?.VITE_API || '/api'

export const useStore = defineStore('store', {
  state: () => ({
    items: [] as any[],
    bundles: [] as any[],
    loading: false,
    error: '' as string,
    adminToken: '' as string
  }),
  actions: {
    async fetchItems() {
      this.loading = true
      try {
        const { data } = await axios.get(`${API}/items`)
        this.items = data
      } catch (e:any) {
        this.error = e?.message || 'Failed to load'
      } finally { this.loading = false }
    },

    async checkout(itemId: number, payload: {email:string,name:string,phone:string}) {
      const { data } = await axios.post(`${API}/checkout`, { item_id: itemId, ...payload })
      return data
    },

    // Admin methods
    setAdminToken(token: string) {
      this.adminToken = token
      // Test the token by making a request to an admin endpoint
      return axios.get(`${API}/admin/items/stats`, {
        headers: { Authorization: `Bearer ${token}` }
      })
    },

    async addProduct(product: any) {
      const { data } = await axios.post(`${API}/items`, product, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      })
      return data
    },

    async updateProductStatus(itemId: number, status: string) {
      const { data } = await axios.post(`${API}/items/${itemId}/status`, { status }, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      })
      return data
    },

    async deleteProduct(itemId: number) {
      await axios.delete(`${API}/items/${itemId}`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      })
    },

    async createBundle(bundle: any) {
      const payload = {
        title: bundle.title,
        description: bundle.description,
        product_ids: bundle.productIds
      }
      const { data } = await axios.post(`${API}/bundles`, payload, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      })
      this.bundles.push(data)
      return data
    },

    async fetchBundles() {
      const { data } = await axios.get(`${API}/bundles`)
      this.bundles = data
    },

    async deleteBundle(bundleId: number) {
      await axios.delete(`${API}/bundles/${bundleId}`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      })
      const index = this.bundles.findIndex(b => b.id === bundleId)
      if (index > -1) {
        this.bundles.splice(index, 1)
      }
    },

    async fetchCategories() {
      const { data } = await axios.get(`${API}/categories`)
      return data.categories
    },

    async createCategory(name: string) {
      const { data } = await axios.post(`${API}/categories`, { name }, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      })
      return data
    },

    async deleteCategory(name: string) {
      await axios.delete(`${API}/categories/${encodeURIComponent(name)}`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      })
    },

    async uploadImages(files: FileList) {
      const formData = new FormData()
      for (let i = 0; i < files.length; i++) {
        formData.append('files', files[i])
      }
      const { data } = await axios.post(`${API}/upload/images`, formData, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      })
      return data
    },

    async uploadVideo(file: File) {
      const formData = new FormData()
      formData.append('file', file)
      const { data } = await axios.post(`${API}/upload/video`, formData, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      })
      return data
    },

    async fetchMarketplaceCredentials() {
      const { data } = await axios.get(`${API}/admin/marketplace-credentials`, {
        headers: { Authorization: `Bearer ${this.adminToken}` }
      })
      return data
    }
  }
})
