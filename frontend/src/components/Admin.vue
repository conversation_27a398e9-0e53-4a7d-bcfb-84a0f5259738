<template>
  <main class="max-w-6xl mx-auto p-6">
    <header class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h1 class="text-3xl font-bold tracking-tight">OneOf Store Admin</h1>
        <router-link to="/" class="text-sm underline">← Back to Store</router-link>
      </div>

      <!-- Admin Navigation Menu -->
      <div class="flex gap-2 border-b">
        <router-link to="/admin" class="px-4 py-2 border-b-2 hover:bg-gray-50"
                     :class="$route.path === '/admin' ? 'border-purple-600 font-semibold' : 'border-transparent'">
          Dashboard
        </router-link>
        <router-link to="/admin/items" class="px-4 py-2 border-b-2 hover:bg-gray-50"
                     :class="$route.path === '/admin/items' ? 'border-purple-600 font-semibold' : 'border-transparent'">
          Edit Items
        </router-link>
        <router-link to="/admin/categories" class="px-4 py-2 border-b-2 hover:bg-gray-50"
                     :class="$route.path === '/admin/categories' ? 'border-purple-600 font-semibold' : 'border-transparent'">
          Categories
        </router-link>
      </div>
    </header>

    <!-- Admin Auth Section -->
    <div v-if="!isAuthenticated" class="bg-white rounded-2xl p-6 shadow mb-6">
      <h2 class="text-xl font-semibold mb-4">Admin Login</h2>
      <form @submit.prevent="login" class="space-y-4">
        <input v-model="adminToken" type="password" placeholder="Admin Bearer Token"
               autocomplete="current-password"
               class="w-full border rounded-lg p-3" required />
        <button type="submit" class="px-6 py-2 bg-black text-white rounded-lg">Login</button>
      </form>
    </div>

    <!-- Admin Panel -->
    <div v-else class="space-y-6">
      <!-- Marketplace Credentials Status -->
      <div class="bg-white rounded-2xl p-6 shadow">
        <h2 class="text-xl font-semibold mb-4">Marketplace Integrations</h2>
        <div v-if="marketplaceCredentials" class="space-y-3">
          <div class="flex items-center justify-between p-3 border rounded-lg">
            <div class="flex items-center gap-3">
              <input type="checkbox" :checked="marketplaceCredentials.credentials.facebook.available" disabled class="rounded" />
              <div>
                <span class="font-medium">Facebook Marketplace</span>
                <p v-if="marketplaceCredentials.credentials.facebook.available" class="text-sm text-gray-600">
                  Connected: {{ marketplaceCredentials.credentials.facebook.email }}
                </p>
                <p v-else class="text-sm text-red-600">Not configured</p>
              </div>
            </div>
            <span :class="marketplaceCredentials.credentials.facebook.available ? 'text-green-600' : 'text-red-600'" class="text-sm font-medium">
              {{ marketplaceCredentials.credentials.facebook.available ? 'Active' : 'Inactive' }}
            </span>
          </div>

          <div class="flex items-center justify-between p-3 border rounded-lg">
            <div class="flex items-center gap-3">
              <input type="checkbox" :checked="marketplaceCredentials.credentials.ebay.available" disabled class="rounded" />
              <div>
                <span class="font-medium">eBay</span>
                <p v-if="marketplaceCredentials.credentials.ebay.available" class="text-sm text-gray-600">
                  Connected: {{ marketplaceCredentials.credentials.ebay.username }}
                </p>
                <p v-else class="text-sm text-red-600">Not configured</p>
              </div>
            </div>
            <span :class="marketplaceCredentials.credentials.ebay.available ? 'text-green-600' : 'text-red-600'" class="text-sm font-medium">
              {{ marketplaceCredentials.credentials.ebay.available ? 'Active' : 'Inactive' }}
            </span>
          </div>

          <div class="flex items-center justify-between p-3 border rounded-lg">
            <div class="flex items-center gap-3">
              <input type="checkbox" :checked="marketplaceCredentials.credentials.craigslist.available" disabled class="rounded" />
              <div>
                <span class="font-medium">Craigslist</span>
                <p v-if="marketplaceCredentials.credentials.craigslist.available" class="text-sm text-gray-600">
                  Connected: {{ marketplaceCredentials.credentials.craigslist.email }}
                </p>
                <p v-else class="text-sm text-red-600">Not configured</p>
              </div>
            </div>
            <span :class="marketplaceCredentials.credentials.craigslist.available ? 'text-green-600' : 'text-red-600'" class="text-sm font-medium">
              {{ marketplaceCredentials.credentials.craigslist.available ? 'Active' : 'Inactive' }}
            </span>
          </div>

          <div class="mt-4 p-3 bg-gray-50 rounded-lg">
            <p class="text-sm text-gray-700">
              <strong>{{ marketplaceCredentials.total_available }}/3</strong> marketplace integrations configured
            </p>
          </div>
        </div>
        <div v-else class="text-gray-500">Loading marketplace status...</div>
      </div>

      <!-- Add New Product -->
      <div class="bg-white rounded-2xl p-6 shadow">
        <h2 class="text-xl font-semibold mb-4">Add New Product</h2>
        <form @submit.prevent="addProduct" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <input v-model="newProduct.sku" placeholder="SKU (optional)" autocomplete="off"
                 class="border rounded-lg p-3" />
          <input v-model="newProduct.title" placeholder="Product Title" autocomplete="off"
                 class="border rounded-lg p-3" required />
          <textarea v-model="newProduct.description" placeholder="Description" rows="3" autocomplete="off"
                    class="border rounded-lg p-3 md:col-span-2"></textarea>
          <input v-model.number="newProduct.price" type="number" step="0.01" placeholder="Price"
                 autocomplete="off" class="border rounded-lg p-3" required />
          <input v-model="newProduct.cover_url" placeholder="Cover Image URL" autocomplete="url"
                 class="border rounded-lg p-3" />
          <select v-model="newProduct.category" class="border rounded-lg p-3">
            <option value="misc">Miscellaneous</option>
            <option value="electronics">Electronics</option>
            <option value="luxury">Luxury</option>
            <option value="home">Home & Garden</option>
            <option value="sports">Sports</option>
          </select>
          <select v-model="newProduct.condition" class="border rounded-lg p-3">
            <option value="new">New</option>
            <option value="used">Used</option>
            <option value="refurbished">Refurbished</option>
            <option value="damaged">Damaged</option>
          </select>
          <input v-model="tagsInput" placeholder="Tags (comma-separated)" autocomplete="off"
                 class="border rounded-lg p-3" />
          <div class="md:col-span-2 flex gap-2">
            <button type="submit" :disabled="addingProduct"
                    class="px-6 py-2 bg-black text-white rounded-lg disabled:opacity-50">
              {{ addingProduct ? 'Adding...' : 'Add Product' }}
            </button>
            <button type="button" @click="resetNewProduct" class="px-6 py-2 border rounded-lg">Reset</button>
          </div>
        </form>
      </div>

      <!-- Manage Existing Products -->
      <div class="bg-white rounded-2xl p-6 shadow">
        <h2 class="text-xl font-semibold mb-4">Manage Products</h2>
        <div class="space-y-4">
          <div v-for="item in store.items" :key="item.id"
               class="border rounded-lg p-4 flex items-center justify-between">
            <div class="flex items-center gap-4">
              <img :src="item.cover_url || 'https://placehold.co/100x100/png'" alt="cover"
                   class="w-16 h-16 rounded-lg object-cover" />
              <div>
                <h3 class="font-semibold">{{ item.title }}</h3>
                <p class="text-sm text-gray-600">${{ item.price.toFixed(2) }}</p>
                <span :class="item.status === 'available' ? 'text-green-600' : 'text-red-600'"
                      class="text-sm font-medium">
                  {{ item.status }}
                </span>
              </div>
            </div>
            <div class="flex gap-2">
              <button @click="toggleStatus(item)"
                      :class="item.status === 'available' ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'"
                      class="px-3 py-1 text-white text-sm rounded">
                {{ item.status === 'available' ? 'Hide' : 'Show' }}
              </button>
              <button @click="deleteProduct(item.id)"
                      class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded">
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Category Management -->
      <div class="bg-white rounded-2xl p-6 shadow">
        <h2 class="text-xl font-semibold mb-4">Category Management</h2>

        <!-- Bulk Actions -->
        <div class="mb-4 p-4 bg-gray-50 rounded-lg space-y-3">
          <h3 class="font-medium">Bulk Actions</h3>

          <!-- Merge Categories -->
          <div class="space-y-2">
            <label class="text-sm font-medium">Merge multiple categories into one:</label>
            <div class="flex gap-2">
              <select v-model="mergeSource" multiple class="flex-1 border rounded-lg p-2" size="3">
                <option v-for="cat in categoriesWithCounts" :key="cat.name" :value="cat.name">
                  {{ cat.name }} ({{ cat.count }})
                </option>
              </select>
              <span class="self-center">→</span>
              <input v-model="mergeTarget" placeholder="Target category name" autocomplete="off"
                     class="flex-1 border rounded-lg p-2" />
              <button @click="performMerge" :disabled="!mergeSource.length || !mergeTarget"
                      class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50">
                Merge
              </button>
            </div>
            <p class="text-xs text-gray-600">Hold Ctrl/Cmd to select multiple categories to merge</p>
          </div>
        </div>

        <!-- Existing Categories -->
        <div class="space-y-2">
          <div v-for="category in categoriesWithCounts" :key="category.name"
               class="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
            <div class="flex-1">
              <div class="flex items-center gap-2">
                <span class="font-medium">{{ category.name }}</span>
                <span class="text-sm px-2 py-0.5 rounded-full"
                      :class="category.count < 3 ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'">
                  {{ category.count }} items
                </span>
              </div>
            </div>
            <div class="flex gap-2">
              <button @click="startRename(category.name)"
                      class="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded">
                Rename
              </button>
              <button @click="deleteCategory(category.name)"
                      class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded">
                Delete
              </button>
            </div>
          </div>
          <div v-if="categoriesWithCounts.length === 0" class="text-gray-500 text-center py-4">
            No categories yet
          </div>
        </div>
      </div>

      <!-- Rename Dialog -->
      <dialog ref="renameDialog" class="rounded-2xl p-6 w-full max-w-md backdrop:bg-black/50">
        <h2 class="text-xl font-semibold mb-4">Rename Category</h2>
        <div class="space-y-3">
          <div>
            <label class="text-sm text-gray-600">Current name:</label>
            <p class="font-medium">{{ renamingCategory }}</p>
          </div>
          <input v-model="renameNewName" placeholder="New category name" autocomplete="off"
                 class="w-full border rounded-lg p-2" @keyup.enter="performRename" />
          <div class="flex gap-2 justify-end">
            <button @click="closeRename" class="px-4 py-2 border rounded-lg hover:bg-gray-50">Cancel</button>
            <button @click="performRename" :disabled="!renameNewName"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50">
              Rename
            </button>
          </div>
        </div>
      </dialog>

      <!-- Confirmation Dialog -->
      <dialog ref="confirmDialog" class="rounded-2xl p-6 w-full max-w-md backdrop:bg-black/50">
        <h2 class="text-xl font-semibold mb-4">{{ confirmTitle }}</h2>
        <p class="text-gray-700 mb-6">{{ confirmMessage }}</p>
        <div class="flex gap-2 justify-end">
          <button @click="closeConfirm" class="px-4 py-2 border rounded-lg hover:bg-gray-50">
            Cancel
          </button>
          <button @click="handleConfirm" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg">
            Confirm
          </button>
        </div>
      </dialog>

      <!-- File Uploads -->
      <div class="bg-white rounded-2xl p-6 shadow">
        <h2 class="text-xl font-semibold mb-4">File Uploads</h2>
        
        <!-- Image Upload -->
        <div class="mb-6">
          <h3 class="text-lg font-medium mb-3">Upload Product Images (Max 10)</h3>
          <input ref="imageInput" type="file" multiple accept="image/*" @change="handleImageSelect" 
                 class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" />
          <div v-if="selectedImages.length > 0" class="mt-3 grid grid-cols-5 gap-2">
            <div v-for="(file, index) in selectedImages" :key="index" class="relative">
              <img :src="getObjectURL(file)" alt="preview" 
                   class="w-full h-20 object-cover rounded-lg" />
              <button @click="removeImage(index)" 
                      class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 text-xs">
                ×
              </button>
            </div>
          </div>
          <button v-if="selectedImages.length > 0" @click="uploadImages" :disabled="uploadingImages"
                  class="mt-3 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50">
            {{ uploadingImages ? 'Uploading...' : `Upload ${selectedImages.length} Images` }}
          </button>
        </div>

        <!-- Video Upload -->
        <div>
          <h3 class="text-lg font-medium mb-3">Upload Product Video (Max 1 minute, 100MB)</h3>
          <input ref="videoInput" type="file" accept="video/*" @change="handleVideoSelect" 
                 class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100" />
          <div v-if="selectedVideo" class="mt-3">
            <video :src="getObjectURL(selectedVideo)" controls class="max-w-md rounded-lg"></video>
            <p class="text-sm text-gray-600 mt-2">{{ selectedVideo.name }} ({{ (selectedVideo.size / 1024 / 1024).toFixed(2) }} MB)</p>
          </div>
          <button v-if="selectedVideo" @click="uploadVideo" :disabled="uploadingVideo"
                  class="mt-3 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg disabled:opacity-50">
            {{ uploadingVideo ? 'Uploading...' : 'Upload Video' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <div v-if="message" :class="messageType === 'error' ? 'text-red-600' : 'text-green-600'"
         class="mt-6 p-4 rounded-lg bg-gray-50">
      {{ message }}
    </div>
  </main>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useStore } from '../stores/useStore'

// No props needed for routing-based admin component

const store = useStore()
const isAuthenticated = ref(false)
const adminToken = ref('')
const message = ref('')
const messageType = ref('')

// New Product Form
const newProduct = reactive({
  sku: '',
  title: '',
  description: '',
  price: 0,
  cover_url: '',
  category: 'misc',
  tags: [] as string[],
  condition: 'new'
})
const addingProduct = ref(false)

// Bundle Form
const bundle = reactive({
  title: '',
  description: '',
  productIds: [] as number[]
})
const creatingBundle = ref(false)
// const bundles = ref([] as any[]) // Removed - using store.bundles

// Category Management
const newCategory = ref('')
const addingCategory = ref(false)
const categories = ref<string[]>([])
const categoriesWithCounts = ref<{name: string, count: number}[]>([])
const renameDialog = ref<HTMLDialogElement | null>(null)
const renamingCategory = ref('')
const renameNewName = ref('')
const mergeSource = ref<string[]>([])
const mergeTarget = ref('')

// Confirmation Dialog
const confirmDialog = ref<HTMLDialogElement | null>(null)
const confirmTitle = ref('')
const confirmMessage = ref('')
const confirmCallback = ref<(() => void) | null>(null)

// File Uploads
const imageInput = ref<HTMLInputElement | null>(null)
const videoInput = ref<HTMLInputElement | null>(null)
const selectedImages = ref<File[]>([])
const selectedVideo = ref<File | null>(null)
const uploadingImages = ref(false)
const uploadingVideo = ref(false)

// Marketplace Credentials
const marketplaceCredentials = ref<any>(null)

// Auth
async function login() {
  try {
    await store.setAdminToken(adminToken.value)
    isAuthenticated.value = true
    message.value = 'Logged in successfully'
    messageType.value = 'success'
    await store.fetchItems()
    await store.fetchBundles()
    await loadMarketplaceCredentials()
  } catch (e: any) {
    message.value = e?.message || 'Login failed'
    messageType.value = 'error'
  }
}

// Product Management
async function addProduct() {
  if (!newProduct.title || newProduct.price <= 0) return

  addingProduct.value = true
  try {
    await store.addProduct(newProduct)
    message.value = 'Product added successfully'
    messageType.value = 'success'
    resetNewProduct()
    await store.fetchItems()
  } catch (e: any) {
    message.value = e?.message || 'Failed to add product'
    messageType.value = 'error'
  } finally {
    addingProduct.value = false
  }
}

function resetNewProduct() {
  Object.assign(newProduct, {
    sku: '',
    title: '',
    description: '',
    price: 0,
    cover_url: '',
    category: 'misc',
    tags: [],
    condition: 'new'
  })
}

async function toggleStatus(item: any) {
  try {
    const newStatus = item.status === 'available' ? 'hidden' : 'available'
    await store.updateProductStatus(item.id, newStatus)
    message.value = `Product ${newStatus === 'available' ? 'shown' : 'hidden'}`
    messageType.value = 'success'
    await store.fetchItems()
  } catch (e: any) {
    message.value = e?.message || 'Failed to update status'
    messageType.value = 'error'
  }
}

async function deleteProduct(itemId: number) {
  showConfirm('Delete Product', 'Are you sure you want to delete this product?', async () => {
    try {
      await store.deleteProduct(itemId)
      message.value = 'Product deleted'
      messageType.value = 'success'
      await store.fetchItems()
    } catch (e: any) {
      message.value = e?.message || 'Failed to delete product'
      messageType.value = 'error'
    }
  })
}

// Bundle Management
async function createBundle() {
  if (!bundle.title || bundle.productIds.length === 0) return

  creatingBundle.value = true
  try {
    await store.createBundle(bundle)
    message.value = 'Bundle created successfully'
    messageType.value = 'success'
    resetBundle()
  } catch (e: any) {
    message.value = e?.message || 'Failed to create bundle'
    messageType.value = 'error'
  } finally {
    creatingBundle.value = false
  }
}

function resetBundle() {
  Object.assign(bundle, {
    title: '',
    description: '',
    productIds: []
  })
}

async function deleteBundle(bundleId: number) {
  showConfirm('Delete Bundle', 'Are you sure you want to delete this bundle?', async () => {
    try {
      await store.deleteBundle(bundleId)
      message.value = 'Bundle deleted'
      messageType.value = 'success'
    } catch (e: any) {
      message.value = e?.message || 'Failed to delete bundle'
      messageType.value = 'error'
    }
  })
}

onMounted(async () => {
  if (store.adminToken) {
    isAuthenticated.value = true
    await store.fetchBundles()
    await loadCategories()
    await loadMarketplaceCredentials()
  }
})

// Category Management
async function loadCategories() {
  try {
    const allCategories = await store.fetchCategories()
    // Show ALL categories, sorted by count descending
    categoriesWithCounts.value = allCategories.sort((a: any, b: any) => b.count - a.count)
    categories.value = categoriesWithCounts.value.map((cat: {name: string}) => cat.name)
  } catch (e: any) {
    message.value = e?.message || 'Failed to load categories'
    messageType.value = 'error'
  }
}


async function deleteCategory(categoryName: string) {
  showConfirm(
    'Delete Category',
    `Are you sure you want to delete the category "${categoryName}"? Items will be moved to "Uncategorized".`,
    async () => {
      try {
        await store.deleteCategory(categoryName)
        message.value = 'Category deleted'
        messageType.value = 'success'
        await loadCategories()
        await store.fetchItems()
      } catch (e: any) {
        message.value = e?.message || 'Failed to delete category'
        messageType.value = 'error'
      }
    }
  )
}

function startRename(categoryName: string) {
  renamingCategory.value = categoryName
  renameNewName.value = categoryName
  renameDialog.value?.showModal()
}

function closeRename() {
  renameDialog.value?.close()
  renamingCategory.value = ''
  renameNewName.value = ''
}

async function performRename() {
  if (!renameNewName.value.trim() || renameNewName.value === renamingCategory.value) {
    closeRename()
    return
  }

  try {
    const result = await store.renameCategory(renamingCategory.value, renameNewName.value.trim())
    message.value = `Renamed "${renamingCategory.value}" to "${renameNewName.value}" (${result.updated} items updated)`
    messageType.value = 'success'
    closeRename()
    await loadCategories()
    await store.fetchItems()
  } catch (e: any) {
    message.value = e?.message || 'Failed to rename category'
    messageType.value = 'error'
  }
}

async function performMerge() {
  if (mergeSource.value.length === 0 || !mergeTarget.value.trim()) return

  showConfirm(
    'Merge Categories',
    `Merge ${mergeSource.value.join(', ')} into "${mergeTarget.value}"?`,
    async () => {
      try {
        const result = await store.mergeCategories(mergeSource.value, mergeTarget.value.trim())
        message.value = `Merged ${result.merged_from.join(', ')} into "${result.merged_to}" (${result.updated} items updated)`
        messageType.value = 'success'
        mergeSource.value = []
        mergeTarget.value = ''
        await loadCategories()
        await store.fetchItems()
      } catch (e: any) {
        message.value = e?.message || 'Failed to merge categories'
        messageType.value = 'error'
      }
    }
  )
}

// File Uploads
function handleImageSelect(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.files) {
    selectedImages.value = Array.from(target.files).slice(0, 10) // Max 10 images
  }
}

function removeImage(index: number) {
  selectedImages.value.splice(index, 1)
}

async function uploadImages() {
  if (selectedImages.value.length === 0) return

  uploadingImages.value = true
  try {
    const result = await store.uploadImages(selectedImages.value as any)
    message.value = `Successfully uploaded ${result.uploaded.length} images`
    messageType.value = 'success'
    
    // Clear selections
    selectedImages.value = []
    if (imageInput.value) {
      imageInput.value.value = ''
    }
  } catch (e: any) {
    message.value = e?.message || 'Failed to upload images'
    messageType.value = 'error'
  } finally {
    uploadingImages.value = false
  }
}

function handleVideoSelect(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    selectedVideo.value = target.files[0]
  }
}

async function uploadVideo() {
  if (!selectedVideo.value) return

  uploadingVideo.value = true
  try {
    const result = await store.uploadVideo(selectedVideo.value)
    message.value = `Video uploaded successfully: ${result.filename}`
    messageType.value = 'success'
    
    // Clear selection
    selectedVideo.value = null
    if (videoInput.value) {
      videoInput.value.value = ''
    }
  } catch (e: any) {
    message.value = e?.message || 'Failed to upload video'
    messageType.value = 'error'
  } finally {
    uploadingVideo.value = false
  }
}

function getObjectURL(file: File): string {
  return URL.createObjectURL(file)
}

// Marketplace Credentials
async function loadMarketplaceCredentials() {
  try {
    marketplaceCredentials.value = await store.fetchMarketplaceCredentials()
  } catch (e: any) {
    message.value = e?.message || 'Failed to load marketplace credentials'
    messageType.value = 'error'
  }
}

// Confirmation Dialog Helpers
function showConfirm(title: string, msg: string, callback: () => void) {
  confirmTitle.value = title
  confirmMessage.value = msg
  confirmCallback.value = callback
  confirmDialog.value?.showModal()
}

function handleConfirm() {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  closeConfirm()
}

function closeConfirm() {
  confirmDialog.value?.close()
  confirmTitle.value = ''
  confirmMessage.value = ''
  confirmCallback.value = null
}

// Computed property for tags input
const tagsInput = computed({
  get: () => newProduct.tags.join(', '),
  set: (value: string) => {
    newProduct.tags = value.split(',').map(tag => tag.trim()).filter(tag => tag)
  }
})
</script>