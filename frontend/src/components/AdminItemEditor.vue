<template>
  <div class="max-w-7xl mx-auto p-6">
    <header class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h1 class="text-3xl font-bold tracking-tight">Item Management</h1>
        <router-link to="/" class="text-sm underline">← Back to Store</router-link>
      </div>

      <!-- Admin Navigation Menu -->
      <div class="flex gap-2 border-b">
        <router-link to="/admin" class="px-4 py-2 border-b-2 hover:bg-gray-50 border-transparent">
          Dashboard
        </router-link>
        <router-link to="/admin/items" class="px-4 py-2 border-b-2 border-purple-600 font-semibold">
          Edit Items
        </router-link>
        <router-link to="/admin/categories" class="px-4 py-2 border-b-2 hover:bg-gray-50 border-transparent">
          Categories
        </router-link>
      </div>
    </header>

    <!-- Admin Token Input -->
    <div v-if="!isAuthenticated" class="mb-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
      <h2 class="text-xl font-semibold mb-4">Admin Authentication</h2>
      <div class="flex gap-3">
        <input
          v-model="adminToken"
          type="password"
          placeholder="Enter admin bearer token"
          class="flex-1 px-4 py-2 border rounded-lg"
          @keyup.enter="authenticate"
        />
        <button
          @click="authenticate"
          class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium"
        >
          Authenticate
        </button>
      </div>
    </div>

    <div v-if="isAuthenticated">
      <!-- Search and Filter -->
      <div class="mb-6 bg-white rounded-lg shadow-md p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search by title, SKU, description..."
            class="px-4 py-2 border rounded-lg"
          />
          <select v-model="filterCategory" class="px-4 py-2 border rounded-lg">
            <option value="">All Categories</option>
            <option v-for="cat in categories" :key="cat.name" :value="cat.name">
              {{ cat.name }} ({{ cat.count }})
            </option>
          </select>
          <select v-model="filterStatus" class="px-4 py-2 border rounded-lg">
            <option value="">All Status</option>
            <option value="available">Available</option>
            <option value="sold">Sold</option>
            <option value="hidden">Hidden</option>
            <option value="reserved">Reserved</option>
          </select>
        </div>
      </div>

      <!-- Items List -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Items ({{ filteredItems.length }})</h2>

        <div class="space-y-3 max-h-[600px] overflow-y-auto">
          <div
            v-for="item in paginatedItems"
            :key="item.id"
            class="flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition"
            @click="editItem(item)"
          >
            <img
              v-if="item.cover_url"
              :src="item.cover_url"
              :alt="item.title"
              class="w-20 h-20 object-cover rounded"
              @error="(e) => e.target.style.display = 'none'"
            />
            <div v-else class="w-20 h-20 bg-gray-200 rounded flex items-center justify-center text-gray-400 text-xs">
              No Image
            </div>
            <div class="flex-1">
              <h3 class="font-semibold text-gray-800">{{ item.title }}</h3>
              <div class="flex items-center gap-3 text-sm text-gray-600">
                <span>SKU: {{ item.sku }}</span>
                <span class="px-2 py-0.5 bg-gray-200 rounded text-xs font-medium">
                  {{ item.category }}
                </span>
                <span :class="statusColor(item.status)" class="px-2 py-0.5 rounded text-xs font-medium">
                  {{ item.status }}
                </span>
                <span class="font-semibold text-green-600">${{ item.price }}</span>
              </div>
            </div>
            <button
              @click.stop="editItem(item)"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Edit
            </button>
            <button
              @click.stop="confirmDelete(item)"
              class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              Delete
            </button>
          </div>
        </div>

        <!-- Pagination -->
        <div class="mt-4 flex justify-between items-center">
          <div class="text-sm text-gray-600">
            Showing {{ (currentPage - 1) * itemsPerPage + 1 }} -
            {{ Math.min(currentPage * itemsPerPage, filteredItems.length) }} of
            {{ filteredItems.length }} items
          </div>
          <div class="flex gap-2">
            <button
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="px-4 py-2 border rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              @click="currentPage++"
              :disabled="currentPage >= totalPages"
              class="px-4 py-2 border rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      </div>

      <!-- Success/Error Messages -->
      <div v-if="message" :class="[
        'p-4 rounded-lg mb-4',
        messageType === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
      ]">
        {{ message }}
      </div>
    </div>

    <!-- Edit Item Modal -->
    <div v-if="showEditModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl my-8">
        <div class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold">Edit Item #{{ editingItem.id }}</h2>
            <button @click="closeEditModal" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
          </div>

          <div class="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
            <!-- Title -->
            <div>
              <label class="block text-sm font-medium mb-1">Title *</label>
              <input
                v-model="editingItem.title"
                type="text"
                class="w-full px-4 py-2 border rounded-lg"
                required
              />
            </div>

            <!-- SKU -->
            <div>
              <label class="block text-sm font-medium mb-1">SKU</label>
              <input
                v-model="editingItem.sku"
                type="text"
                class="w-full px-4 py-2 border rounded-lg bg-gray-100"
                readonly
              />
            </div>

            <!-- Price and Condition -->
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium mb-1">Price *</label>
                <input
                  v-model.number="editingItem.price"
                  type="number"
                  step="0.01"
                  class="w-full px-4 py-2 border rounded-lg"
                  required
                />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">Condition</label>
                <select v-model="editingItem.condition" class="w-full px-4 py-2 border rounded-lg">
                  <option value="new">New</option>
                  <option value="Good">Good</option>
                  <option value="Excellent">Excellent</option>
                  <option value="used">Used</option>
                  <option value="For Parts">For Parts</option>
                  <option value="refurbished">Refurbished</option>
                </select>
              </div>
            </div>

            <!-- Category and Status -->
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium mb-1">Category</label>
                <select v-model="editingItem.category" class="w-full px-4 py-2 border rounded-lg">
                  <option v-for="cat in categories" :key="cat.name" :value="cat.name">
                    {{ cat.name }}
                  </option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">Status</label>
                <select v-model="editingItem.status" class="w-full px-4 py-2 border rounded-lg">
                  <option value="available">Available</option>
                  <option value="reserved">Reserved</option>
                  <option value="sold">Sold</option>
                  <option value="hidden">Hidden</option>
                </select>
              </div>
            </div>

            <!-- Description -->
            <div>
              <label class="block text-sm font-medium mb-1">Description</label>
              <textarea
                v-model="editingItem.description"
                rows="4"
                class="w-full px-4 py-2 border rounded-lg"
              ></textarea>
            </div>

            <!-- Cover Image URL -->
            <div>
              <label class="block text-sm font-medium mb-1">Cover Image URL</label>
              <input
                v-model="editingItem.cover_url"
                type="text"
                class="w-full px-4 py-2 border rounded-lg"
                placeholder="https://example.com/image.jpg"
              />
              <img
                v-if="editingItem.cover_url"
                :src="editingItem.cover_url"
                :alt="editingItem.title"
                class="mt-2 w-32 h-32 object-cover rounded border"
                @error="(e) => { e.target.style.display = 'none'; editingItem.cover_url = '' }"
              />
            </div>

            <!-- Gallery URLs -->
            <div>
              <label class="block text-sm font-medium mb-1">Gallery Images (one URL per line)</label>
              <textarea
                v-model="galleryText"
                rows="4"
                class="w-full px-4 py-2 border rounded-lg font-mono text-sm"
                placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg"
              ></textarea>
              <div v-if="editingItem.gallery && editingItem.gallery.length > 0" class="mt-2 flex gap-2 flex-wrap">
                <img
                  v-for="(url, idx) in editingItem.gallery"
                  :key="idx"
                  :src="url"
                  class="w-20 h-20 object-cover rounded border"
                />
              </div>
            </div>

            <!-- Tags -->
            <div>
              <label class="block text-sm font-medium mb-1">Tags (comma-separated)</label>
              <input
                v-model="tagsText"
                type="text"
                class="w-full px-4 py-2 border rounded-lg"
                placeholder="facebook, marketplace, vintage"
              />
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-3 justify-end mt-6 pt-4 border-t">
            <button
              @click="closeEditModal"
              class="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 font-medium"
            >
              Cancel
            </button>
            <button
              @click="saveItem"
              class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4 text-red-600">Confirm Delete</h2>
        <p class="mb-6">Are you sure you want to delete this item?</p>
        <p class="font-semibold mb-6">{{ itemToDelete?.title }}</p>
        <div class="flex gap-3 justify-end">
          <button
            @click="showDeleteModal = false"
            class="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 font-medium"
          >
            Cancel
          </button>
          <button
            @click="deleteItem"
            class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import axios from 'axios'

const API = import.meta.env.VITE_API || '/api'

const adminToken = ref('')
const isAuthenticated = ref(false)
const items = ref<any[]>([])
const categories = ref<any[]>([])
const searchQuery = ref('')
const filterCategory = ref('')
const filterStatus = ref('')
const message = ref('')
const messageType = ref<'success' | 'error'>('success')
const currentPage = ref(1)
const itemsPerPage = 20

const showEditModal = ref(false)
const editingItem = ref<any>({})
const galleryText = ref('')
const tagsText = ref('')

const showDeleteModal = ref(false)
const itemToDelete = ref<any>(null)

const filteredItems = computed(() => {
  let result = items.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item =>
      item.title.toLowerCase().includes(query) ||
      item.sku.toLowerCase().includes(query) ||
      (item.description && item.description.toLowerCase().includes(query))
    )
  }

  if (filterCategory.value) {
    result = result.filter(item => item.category === filterCategory.value)
  }

  if (filterStatus.value) {
    result = result.filter(item => item.status === filterStatus.value)
  }

  return result
})

const totalPages = computed(() => Math.ceil(filteredItems.value.length / itemsPerPage))

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredItems.value.slice(start, end)
})

function statusColor(status: string) {
  const colors = {
    available: 'bg-green-100 text-green-800',
    sold: 'bg-gray-100 text-gray-800',
    hidden: 'bg-yellow-100 text-yellow-800',
    reserved: 'bg-blue-100 text-blue-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

async function authenticate() {
  try {
    const response = await axios.get(`${API}/admin/items/stats`, {
      headers: { Authorization: `Bearer ${adminToken.value}` }
    })
    if (response.data) {
      isAuthenticated.value = true
      localStorage.setItem('admin_token', adminToken.value)
      await fetchData()
      showMessage('Authenticated successfully!', 'success')
    }
  } catch (error: any) {
    showMessage('Authentication failed: ' + (error.response?.data?.detail || 'Invalid token'), 'error')
  }
}

async function fetchData() {
  try {
    const [itemsRes, categoriesRes] = await Promise.all([
      axios.get(`${API}/items`),
      axios.get(`${API}/categories`)
    ])
    items.value = itemsRes.data
    categories.value = categoriesRes.data.categories
  } catch (error) {
    console.error('Failed to fetch data:', error)
  }
}

function editItem(item: any) {
  editingItem.value = { ...item }
  galleryText.value = (item.gallery || []).join('\n')
  tagsText.value = (item.tags || []).join(', ')
  showEditModal.value = true
}

function closeEditModal() {
  showEditModal.value = false
  editingItem.value = {}
  galleryText.value = ''
  tagsText.value = ''
}

async function saveItem() {
  try {
    // Parse gallery and tags
    const gallery = galleryText.value.split('\n').map(s => s.trim()).filter(s => s)
    const tags = tagsText.value.split(',').map(s => s.trim()).filter(s => s)

    const itemData = {
      ...editingItem.value,
      gallery,
      tags
    }

    const response = await axios.put(
      `${API}/items/${editingItem.value.id}`,
      itemData,
      { headers: { Authorization: `Bearer ${adminToken.value}` } }
    )

    showMessage('Item updated successfully!', 'success')
    closeEditModal()
    await fetchData()
  } catch (error: any) {
    showMessage('Failed to update item: ' + (error.response?.data?.detail || 'Unknown error'), 'error')
  }
}

function confirmDelete(item: any) {
  itemToDelete.value = item
  showDeleteModal.value = true
}

async function deleteItem() {
  try {
    await axios.delete(
      `${API}/items/${itemToDelete.value.id}`,
      { headers: { Authorization: `Bearer ${adminToken.value}` } }
    )

    showMessage('Item deleted successfully!', 'success')
    showDeleteModal.value = false
    itemToDelete.value = null
    await fetchData()
  } catch (error: any) {
    showMessage('Failed to delete item: ' + (error.response?.data?.detail || 'Unknown error'), 'error')
  }
}

function showMessage(msg: string, type: 'success' | 'error') {
  message.value = msg
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 5000)
}

onMounted(() => {
  const savedToken = localStorage.getItem('admin_token')
  if (savedToken) {
    adminToken.value = savedToken
  }
})
</script>
