<template>
  <div class="max-w-6xl mx-auto p-6">
    <header class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h1 class="text-3xl font-bold tracking-tight">Category Management</h1>
        <router-link to="/" class="text-sm underline">← Back to Store</router-link>
      </div>

      <!-- Admin Navigation Menu -->
      <div class="flex gap-2 border-b">
        <router-link to="/admin" class="px-4 py-2 border-b-2 hover:bg-gray-50 border-transparent">
          Dashboard
        </router-link>
        <router-link to="/admin/items" class="px-4 py-2 border-b-2 hover:bg-gray-50 border-transparent">
          Edit Items
        </router-link>
        <router-link to="/admin/categories" class="px-4 py-2 border-b-2 border-purple-600 font-semibold">
          Categories
        </router-link>
      </div>
    </header>

    <!-- Admin Token Input -->
    <div v-if="!isAuthenticated" class="mb-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
      <h2 class="text-xl font-semibold mb-4">Admin Authentication</h2>
      <div class="flex gap-3">
        <input
          v-model="adminToken"
          type="password"
          placeholder="Enter admin bearer token"
          class="flex-1 px-4 py-2 border rounded-lg"
          @keyup.enter="authenticate"
        />
        <button
          @click="authenticate"
          class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium"
        >
          Authenticate
        </button>
      </div>
    </div>

    <div v-if="isAuthenticated">
      <!-- Category Stats -->
      <div class="mb-8 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div
          v-for="category in categories"
          :key="category.name"
          class="bg-white rounded-lg shadow p-4 border-l-4"
          :class="getCategoryColor(category.name)"
        >
          <h3 class="font-semibold text-gray-800 mb-1">{{ category.name }}</h3>
          <p class="text-2xl font-bold text-gray-900">{{ category.count }}</p>
          <p class="text-sm text-gray-600">${{ Math.round(category.total_value) }} total</p>
        </div>
      </div>

      <!-- Bulk Category Assignment -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Bulk Category Assignment</h2>

        <!-- Search Items -->
        <div class="mb-4">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search items by title, SKU, or description..."
            class="w-full px-4 py-2 border rounded-lg"
          />
        </div>

        <!-- Category Filter -->
        <div class="mb-4 flex flex-wrap gap-2">
          <button
            @click="filterCategory = null"
            :class="[
              'px-4 py-2 rounded-lg font-medium',
              filterCategory === null
                ? 'bg-purple-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            ]"
          >
            All ({{ filteredItems.length }})
          </button>
          <button
            v-for="cat in categories"
            :key="cat.name"
            @click="filterCategory = cat.name"
            :class="[
              'px-4 py-2 rounded-lg font-medium',
              filterCategory === cat.name
                ? 'bg-purple-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            ]"
          >
            {{ cat.name }} ({{ cat.count }})
          </button>
        </div>

        <!-- Selected Items Actions -->
        <div v-if="selectedItems.length > 0" class="mb-4 bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <span class="font-semibold text-purple-900">
              {{ selectedItems.length }} items selected
            </span>
            <div class="flex gap-2">
              <select
                v-model="targetCategory"
                class="px-4 py-2 border rounded-lg font-medium"
              >
                <option value="">Select category...</option>
                <option v-for="cat in categories" :key="cat.name" :value="cat.name">
                  {{ cat.name }}
                </option>
              </select>
              <button
                @click="assignCategory"
                :disabled="!targetCategory"
                class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium disabled:bg-gray-400"
              >
                Assign Category
              </button>
              <button
                @click="clearSelection"
                class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 font-medium"
              >
                Clear
              </button>
            </div>
          </div>
        </div>

        <!-- Items List -->
        <div class="space-y-2 max-h-[600px] overflow-y-auto">
          <div
            v-for="item in paginatedItems"
            :key="item.id"
            :class="[
              'flex items-center gap-4 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition',
              selectedItems.includes(item.id) ? 'bg-purple-50 border-purple-300' : ''
            ]"
            @click="toggleItem(item.id)"
          >
            <input
              type="checkbox"
              :checked="selectedItems.includes(item.id)"
              class="w-5 h-5"
              @click.stop="toggleItem(item.id)"
            />
            <img
              v-if="item.cover_url"
              :src="item.cover_url"
              :alt="item.title"
              class="w-16 h-16 object-cover rounded"
            />
            <div v-else class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center text-gray-400">
              No Image
            </div>
            <div class="flex-1">
              <h3 class="font-semibold text-gray-800">{{ item.title }}</h3>
              <div class="flex items-center gap-3 text-sm text-gray-600">
                <span>SKU: {{ item.sku }}</span>
                <span class="px-2 py-0.5 bg-gray-200 rounded text-xs font-medium">
                  {{ item.category || 'Uncategorized' }}
                </span>
                <span class="font-semibold text-green-600">${{ item.price }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div class="mt-4 flex justify-between items-center">
          <div class="text-sm text-gray-600">
            Showing {{ (currentPage - 1) * itemsPerPage + 1 }} -
            {{ Math.min(currentPage * itemsPerPage, filteredItems.length) }} of
            {{ filteredItems.length }} items
          </div>
          <div class="flex gap-2">
            <button
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="px-4 py-2 border rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              @click="currentPage++"
              :disabled="currentPage >= totalPages"
              class="px-4 py-2 border rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      </div>

      <!-- Success/Error Messages -->
      <div v-if="message" :class="[
        'p-4 rounded-lg mb-4',
        messageType === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
      ]">
        {{ message }}
      </div>

      <!-- Create New Category -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Create New Category</h2>
        <div class="flex gap-3">
          <input
            v-model="newCategoryName"
            type="text"
            placeholder="Category name (e.g., 'Furniture')"
            class="flex-1 px-4 py-2 border rounded-lg"
            @keyup.enter="createCategory"
          />
          <button
            @click="createCategory"
            class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
          >
            Create Category
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'

const API = import.meta.env.VITE_API || '/api'

const adminToken = ref('')
const isAuthenticated = ref(false)
const categories = ref<any[]>([])
const items = ref<any[]>([])
const selectedItems = ref<number[]>([])
const targetCategory = ref('')
const searchQuery = ref('')
const filterCategory = ref<string | null>(null)
const message = ref('')
const messageType = ref<'success' | 'error'>('success')
const newCategoryName = ref('')
const currentPage = ref(1)
const itemsPerPage = 20

const filteredItems = computed(() => {
  let result = items.value

  // Filter by category
  if (filterCategory.value) {
    result = result.filter(item => item.category === filterCategory.value)
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item =>
      item.title.toLowerCase().includes(query) ||
      item.sku.toLowerCase().includes(query) ||
      (item.description && item.description.toLowerCase().includes(query))
    )
  }

  return result
})

const totalPages = computed(() => Math.ceil(filteredItems.value.length / itemsPerPage))

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredItems.value.slice(start, end)
})

function getCategoryColor(name: string) {
  const colors = [
    'border-blue-500',
    'border-green-500',
    'border-purple-500',
    'border-orange-500',
    'border-pink-500',
    'border-yellow-500',
    'border-indigo-500',
    'border-red-500'
  ]
  const index = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
  return colors[index % colors.length]
}

async function authenticate() {
  try {
    // Test the token with a simple API call
    const response = await axios.get(`${API}/admin/items/stats`, {
      headers: { Authorization: `Bearer ${adminToken.value}` }
    })
    if (response.data) {
      isAuthenticated.value = true
      await fetchData()
      showMessage('Authenticated successfully!', 'success')
    }
  } catch (error: any) {
    showMessage('Authentication failed: ' + (error.response?.data?.detail || 'Invalid token'), 'error')
  }
}

async function fetchData() {
  try {
    const [categoriesRes, itemsRes] = await Promise.all([
      axios.get(`${API}/categories`),
      axios.get(`${API}/items`)
    ])
    categories.value = categoriesRes.data.categories
    items.value = itemsRes.data
  } catch (error) {
    console.error('Failed to fetch data:', error)
  }
}

function toggleItem(itemId: number) {
  const index = selectedItems.value.indexOf(itemId)
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(itemId)
  }
}

function clearSelection() {
  selectedItems.value = []
  targetCategory.value = ''
}

async function assignCategory() {
  if (!targetCategory.value || selectedItems.value.length === 0) return

  try {
    const response = await axios.post(
      `${API}/admin/items/assign-category`,
      {
        item_ids: selectedItems.value,
        category: targetCategory.value
      },
      { headers: { Authorization: `Bearer ${adminToken.value}` } }
    )

    showMessage(
      `Successfully assigned ${response.data.updated} items to ${targetCategory.value}`,
      'success'
    )

    // Refresh data
    await fetchData()
    clearSelection()
  } catch (error: any) {
    showMessage('Failed to assign category: ' + (error.response?.data?.detail || 'Unknown error'), 'error')
  }
}

async function createCategory() {
  if (!newCategoryName.value.trim()) return

  try {
    await axios.post(
      `${API}/categories`,
      { name: newCategoryName.value },
      { headers: { Authorization: `Bearer ${adminToken.value}` } }
    )

    showMessage(`Category "${newCategoryName.value}" created successfully!`, 'success')
    newCategoryName.value = ''
    await fetchData()
  } catch (error: any) {
    showMessage('Failed to create category: ' + (error.response?.data?.detail || 'Unknown error'), 'error')
  }
}

function showMessage(msg: string, type: 'success' | 'error') {
  message.value = msg
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 5000)
}

onMounted(() => {
  // Auto-fill token if in localStorage (for development)
  const savedToken = localStorage.getItem('admin_token')
  if (savedToken) {
    adminToken.value = savedToken
  }
})
</script>
