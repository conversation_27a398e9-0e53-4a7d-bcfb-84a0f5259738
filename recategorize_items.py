#!/usr/bin/env python3
"""
Smart recategorization script for OneOf Store
Analyzes item titles and assigns correct categories
"""

import requests
import json
import re

API = "https://oneofstore.com/api"
TOKEN = "changeme-super-secret-admin-api-bearer-moskva17"

# Category mapping rules - order matters (more specific first)
CATEGORY_RULES = {
    "Jewelry and Watches": [
        r'\bwatch\b', r'\bbracelet\b', r'\bring\b', r'\bnecklace\b',
        r'\bjewelry\b', r'\bgold\b.*\btone\b', r'\bsilver\b.*\btone\b',
        r'\bquartz\b.*\bwatch\b', r'\bmovado\b', r'\blexington\b.*\bwatch\b',
        r'\bpiccard\b', r'\bchayka\b', r'\bbutterfly.*watch\b',
        r'\bbangle\b', r'\bembellished\b.*\bbracelet\b'
    ],
    "Car Audio": [
        r'\bcar\b.*\baudio\b', r'\bamplifier\b', r'\bsubwoofer\b',
        r'\bspeaker\b', r'\bcapacitor\b.*\bfarad\b', r'\bfuse\b.*\bblock\b',
        r'\bjl\b.*\baudio\b', r'\baudiocontrol\b', r'\bepicenter\b',
        r'\bjvc\b.*\bpower\b', r'\bkinter\b', r'\bmini\b.*\bamplifier\b',
        r'\b2farad\b', r'\bcar.*capacitor\b'
    ],
    "Car/Truck/Motorcycle Parts": [
        r'\bhubcap', r'\bcatalytic\b.*\bconverter\b', r'\bhelmet\b',
        r'\bburner\b.*\btube\b', r'\bgrill\b', r'\bair\b.*\bbrush\b',
        r'\bvega\b.*\bnt200\b', r'\bpulcherflow\b', r'\btesla\b.*\btrunk\b',
        r'\bmotorcycle\b', r'\bauto\b.*\bpart\b', r'\bvehicle\b.*\bpart\b'
    ],
    "Computer Parts": [
        r'\bcomputer\b.*\bcable', r'\bmotherboard\b', r'\bcpu\b',
        r'\bpower\b.*\bsupply\b.*\bcable', r'\bdell\b.*\bserver\b',
        r'\bevga\b', r'\bbreakboard\b', r'\bserver\b.*\bpower\b',
        r'\b1100w\b.*\bpower\b'
    ],
    "Tools": [
        r'\btool\b', r'\bsaw\b', r'\bdrill\b', r'\bwrench\b',
        r'\bclamp\b', r'\bgripper\b', r'\bpushblock\b', r'\bmagic\b.*\bsaw\b',
        r'\bmilling\b.*\battachment\b', r'\blumber\b.*\bmilling\b',
        r'\bparallel\b.*\bclamp\b', r'\bhold\b.*\bdown\b.*\bclamp\b',
        r'\bmicrojig\b', r'\bmilwaukee\b.*\bcharger\b'
    ],
    "Sports": [
        r'\bbasketball\b', r'\bbicycle\b', r'\bbike\b', r'\brunning\b.*\bshoe',
        r'\bweight\b', r'\bfitness\b', r'\bsporting\b', r'\bhoop\b.*\brim\b',
        r'\bmicroloading\b.*\bweight\b', r'\bmountain\b.*\bbike\b',
        r'\bski\b', r'\bskiing\b', r'\bfin\b', r'\bswimming\b',
        r'\blongboard\b', r'\bfoot\b.*\brocker\b'
    ],
    "Household Items": [
        r'\bchair\b', r'\btable\b', r'\bfurniture\b', r'\bcabinet\b',
        r'\bstorage\b.*\bbox', r'\borganizer\b', r'\bpull.*out\b.*\bcabinet\b',
        r'\boffice\b.*\bchair\b', r'\bcoat\b', r'\bwinter\b.*\bcoat\b',
        r'\bdoor\b.*\block\b', r'\bquickset\b'
    ],
    "Toys": [
        r'\btoy\b', r'\bdoll\b', r'\bdollhouse\b', r'\bkid\b',
        r'\bkidkraft\b', r'\bcottage\b.*\bdollhouse\b'
    ],
    "Books": [
        r'\bbook\b', r'\bnovel\b', r'\bmagazine\b', r'\brecord\b.*\bcollection\b',
        r'\bvinyl\b.*\brecord\b'
    ],
    "Electronics": [
        r'\bcable\b', r'\bcharger\b', r'\binverter\b', r'\belectronic\b',
        r'\bhdmi\b', r'\bsplitter\b', r'\bkvm\b', r'\bcompressor\b',
        r'\bmini\b.*\bair\b.*\bcompressor\b', r'\blaser\b.*\blevel\b'
    ],
    "Materials": [
        r'\blumber\b', r'\bwood\b', r'\bmetal\b', r'\bmaterial\b'
    ]
}

def categorize_item(title, current_category):
    """Determine the best category for an item based on its title"""
    title_lower = title.lower()

    # Check each category's rules
    for category, patterns in CATEGORY_RULES.items():
        for pattern in patterns:
            if re.search(pattern, title_lower, re.IGNORECASE):
                return category

    # If no match, keep current category or use "Miscellaneous"
    if current_category in CATEGORY_RULES.keys():
        return current_category
    return "Miscellaneous"

def main():
    print("Fetching all items...")
    response = requests.get(f"{API}/items")
    items = response.json()

    print(f"Found {len(items)} items")

    # Categorize each item
    categorization = {}
    for item in items:
        item_id = item['id']
        title = item['title']
        current_cat = item.get('category', 'Unknown')

        # Skip placeholder category items
        if title.startswith("Category:"):
            continue

        new_cat = categorize_item(title, current_cat)

        if new_cat not in categorization:
            categorization[new_cat] = []

        categorization[new_cat].append({
            'id': item_id,
            'title': title,
            'old_category': current_cat,
            'new_category': new_cat
        })

    # Print summary
    print("\n=== CATEGORIZATION SUMMARY ===")
    for cat, items_list in sorted(categorization.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"\n{cat}: {len(items_list)} items")
        for item in items_list[:3]:
            print(f"  - {item['title'][:60]}")
        if len(items_list) > 3:
            print(f"  ... and {len(items_list) - 3} more")

    # Ask for confirmation
    print("\n" + "="*60)
    response = input("Apply these changes? (yes/no): ")

    if response.lower() != 'yes':
        print("Aborted.")
        return

    # Apply categorization
    print("\nApplying categorization...")
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }

    changes_made = 0
    errors = 0

    for category, items_list in categorization.items():
        item_ids = [item['id'] for item in items_list]

        if not item_ids:
            continue

        # Bulk assign category
        try:
            response = requests.post(
                f"{API}/admin/items/assign-category",
                headers=headers,
                json={
                    "item_ids": item_ids,
                    "category": category
                }
            )

            if response.status_code == 200:
                result = response.json()
                changes_made += result.get('updated', 0)
                print(f"✓ {category}: {result.get('updated', 0)} items updated")
            else:
                print(f"✗ {category}: Error - {response.text}")
                errors += 1
        except Exception as e:
            print(f"✗ {category}: Exception - {e}")
            errors += 1

    print(f"\n=== COMPLETE ===")
    print(f"Total items recategorized: {changes_made}")
    print(f"Errors: {errors}")

if __name__ == "__main__":
    main()
