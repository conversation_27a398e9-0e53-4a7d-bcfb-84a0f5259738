# Chrome Extension Update - Share Link Support

## What Changed

Facebook has changed their URL structure from `/item/123456789` to `/share/XXXXXXXX` format for marketplace listings.

### Updated Files

1. **content.js** - Main extraction script
   - Now looks for `/share/` links as the PRIMARY method
   - Extracts share IDs like "1Ly2NariUx" from URLs
   - Creates SKUs as "FB-SHARE-XXXXXXXX" for share links
   - Falls back to old methods (/item/, edit links, HTML scraping) if no share links found

2. **DEBUG_LINKS_V2.js** - Diagnostic script
   - Updated to test for /share/ links first
   - Shows sample share URLs found on page
   - Reports total across all methods

## How to Test

### Step 1: Reload Extension

1. Open Chrome: `chrome://extensions/`
2. Find "OneOf Store Extension"
3. Click the refresh icon (🔄) to reload
4. Confirm it says "Enabled"

### Step 2: Run Debug Script

1. Navigate to: https://www.facebook.com/marketplace/you/selling
2. Scroll down to load all your listings
3. Open DevTools (F12)
4. Go to Console tab
5. Copy and paste the entire contents of `DEBUG_LINKS_V2.js`
6. Press Enter

### Step 3: Check Debug Output

You should see:

```
================================================================================
OneOf Store Extension - Debug Link Detection
================================================================================
Current URL: https://www.facebook.com/marketplace/you/selling
Total links: 532

METHOD 1: /share/ links (NEW FORMAT)
Found: 24
Sample IDs: ['1Ly2NariUx', '2Bx3KpqWmZ', '9Cx8YtrLpQ']
Sample URLs: [
  'https://www.facebook.com/share/1Ly2NariUx',
  'https://www.facebook.com/share/2Bx3KpqWmZ',
  'https://www.facebook.com/share/9Cx8YtrLpQ'
]

METHOD 2: Edit links (listing_id=)
Found: 0

METHOD 3: /item/ links (OLD FORMAT)
Found: 0

METHOD 4: HTML source search
Found: 0

================================================================================
TOTAL UNIQUE LISTINGS: 24
  - Share links (/share/): 24
  - Numeric IDs (/item/, edit, HTML): 0
✅ Would extract 24 items
Sample share URLs: [
  'https://www.facebook.com/share/1Ly2NariUx',
  'https://www.facebook.com/share/2Bx3KpqWmZ',
  'https://www.facebook.com/share/9Cx8YtrLpQ'
]
================================================================================
```

### Step 4: Run Extension

If debug script shows listings found:

1. Click the OneOf Store extension icon
2. Configure settings (API URL, token)
3. Click "Extract & Import"
4. Watch the console for progress

## What Gets Extracted from Share Pages

The extension will visit each `/share/XXXXXXXX` URL and extract:

- ✅ **Title** - From H1 or prominent text
- ✅ **Price** - Pattern match for $XX.XX
- ✅ **Description** - Longer text blocks
- ✅ **ALL Images** - From Facebook's scontent CDN
- ✅ **Video** - If present
- ✅ **Condition** - Auto-detected from text (New/Used)

## SKU Format

- **Share links**: `FB-SHARE-1Ly2NariUx`
- **Old item links**: `FB-123456789`

## Example Share Link

```
https://www.facebook.com/share/1Ly2NariUx/
```

This will be:
- Extracted as ID: `SHARE-1Ly2NariUx`
- Stored with SKU: `FB-SHARE-1Ly2NariUx`
- Navigated to: `https://www.facebook.com/share/1Ly2NariUx`

## Troubleshooting

### "No listings found"

1. Make sure debug script shows listings (Method 1 should have count > 0)
2. Reload extension (chrome://extensions → refresh icon)
3. Try scrolling down manually on selling page
4. Check that you have published, active listings (not drafts)

### "Import failed"

1. Check API URL is correct (https://oneofstore.com/api)
2. Verify admin token is valid
3. Check network connectivity
4. Look at browser console for specific error

### Debug script shows 0 share links

Facebook may have changed the structure again. Try:

1. Right-click any listing → "Copy link"
2. Paste the link - does it look like `/share/XXXXX`?
3. If yes, check if the extension is looking for the right pattern
4. If no, Facebook changed format again - need to investigate

## Next Steps

1. ✅ Run debug script to confirm share links are found
2. ✅ Reload extension
3. ✅ Test extraction on 1-2 listings
4. ✅ Verify data appears on OneOfStore
5. ✅ Run full extraction on all listings

## Support

If issues persist after following these steps, provide:

1. Debug script output (from console)
2. Example share link from your listing
3. Browser console errors (if any)
4. What step failed
