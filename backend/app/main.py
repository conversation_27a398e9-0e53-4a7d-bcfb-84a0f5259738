import os, json, uuid, time, shutil
from pathlib import Path
from fastapi import FastAP<PERSON>, Depends, HTTPException, UploadFile, File, Form, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from .db import SessionLocal, init_db
from . import models, schemas
import stripe
import paypalrestsdk
from dotenv import load_dotenv

# Load environment variables
load_dotenv(Path(__file__).parent.parent.parent / ".env")

# Create uploads directory
UPLOAD_DIR = Path(__file__).parent.parent / "uploads"
UPLOAD_DIR.mkdir(exist_ok=True)

init_db()

app = FastAPI(title=os.getenv("STORE_NAME", "OneOf Store"))

origins = os.getenv("ALLOWED_ORIGINS", "").split(",") if os.getenv("ALLOWED_ORIGINS") else ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

ADMIN_TOKEN = os.getenv("ADMIN_API_BEARER", "changeme-super-secret")

STRIPE_KEY = os.getenv("STRIPE_SECRET_KEY")
STRIPE_PUBLISHABLE_KEY = os.getenv("STRIPE_PUBLISHABLE_KEY")
if STRIPE_KEY:
    stripe.api_key = STRIPE_KEY

PAYPAL_CLIENT_ID = os.getenv("PAYPAL_CLIENT_ID")
PAYPAL_CLIENT_SECRET = os.getenv("PAYPAL_CLIENT_SECRET")
PAYPAL_MODE = os.getenv("PAYPAL_MODE", "sandbox")  # sandbox or live

if PAYPAL_CLIENT_ID and PAYPAL_CLIENT_SECRET:
    paypalrestsdk.configure({
        "mode": PAYPAL_MODE,
        "client_id": PAYPAL_CLIENT_ID,
        "client_secret": PAYPAL_CLIENT_SECRET
    })

# Dependency

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Simple admin auth

def admin_guard(authorization: str | None = Header(None)):
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing Bearer token")
    token = authorization.split(" ", 1)[1]
    if token != ADMIN_TOKEN:
        raise HTTPException(status_code=403, detail="Invalid admin token")

@app.get("/health")
def health():
    return {"ok": True}

@app.get("/config")
def get_config():
    """Public endpoint to get client-side configuration"""
    return {
        "stripe_publishable_key": STRIPE_PUBLISHABLE_KEY,
        "paypal_client_id": PAYPAL_CLIENT_ID
    }

# Items
@app.get("/items", response_model=list[schemas.ItemOut])
def list_items(db: Session = Depends(get_db)):
    rows = db.query(models.Item).order_by(models.Item.created_at.desc()).all()
    out = []
    for r in rows:
        # Preprocess the data to match schema expectations
        item_data = {
            "id": r.id,
            "sku": r.sku,
            "title": r.title,
            "description": r.description or "",
            "price": r.price,
            "status": r.status,
            "cover_url": r.cover_url or "",
            "gallery": [g for g in r.gallery.split(",") if g] if r.gallery else [],  # type: ignore
            "category": r.category or "misc",
            "tags": [t for t in r.tags.split(",") if t] if r.tags else [],  # type: ignore
            "condition": r.condition or "new",
            "marketplace_refs": json.loads(r.marketplace_refs) if r.marketplace_refs else None  # type: ignore
        }
        o = schemas.ItemOut(**item_data)
        out.append(o)
    return out

@app.post("/items", response_model=schemas.ItemOut, dependencies=[Depends(admin_guard)])
def create_item(item: schemas.ItemIn, db: Session = Depends(get_db)):
    # Store fb_url in marketplace_refs as JSON
    marketplace_refs = {}
    if item.fb_url:
        marketplace_refs["facebook"] = item.fb_url

    m = models.Item(
        sku=item.sku,
        title=item.title,
        description=item.description or "",
        price=item.price,
        cover_url=item.cover_url or "",
        gallery=",".join(item.gallery or []),
        category=item.category or "misc",
        tags=",".join(item.tags or []),
        condition=item.condition or "new",
        marketplace_refs=json.dumps(marketplace_refs) if marketplace_refs else ""
    )
    db.add(m)
    db.commit()
    db.refresh(m)

    # Convert database fields to schema format
    item_data = {
        "id": m.id,
        "sku": m.sku,
        "title": m.title,
        "description": m.description or "",
        "price": m.price,
        "status": m.status,
        "cover_url": m.cover_url or "",
        "gallery": [g for g in m.gallery.split(",") if g] if m.gallery else [],
        "category": m.category or "misc",
        "tags": [t for t in m.tags.split(",") if t] if m.tags else [],
        "condition": m.condition or "new",
        "marketplace_refs": json.loads(m.marketplace_refs) if m.marketplace_refs else None
    }
    return schemas.ItemOut(**item_data)

@app.patch("/items/{item_id}", response_model=schemas.ItemOut, dependencies=[Depends(admin_guard)])
@app.put("/items/{item_id}", response_model=schemas.ItemOut, dependencies=[Depends(admin_guard)])
def update_item(item_id: int, item: schemas.ItemIn, db: Session = Depends(get_db)):
    m = db.query(models.Item).get(item_id)
    if not m:
        raise HTTPException(404, "Item not found")

    # Handle fb_url in marketplace_refs
    marketplace_refs = {}
    if m.marketplace_refs:
        try:
            marketplace_refs = json.loads(m.marketplace_refs)
        except:
            marketplace_refs = {}

    if item.fb_url:
        marketplace_refs["facebook"] = item.fb_url

    m.sku=item.sku; m.title=item.title; m.description=item.description or ""; m.price=item.price
    m.cover_url=item.cover_url or ""; m.gallery=",".join(item.gallery or [])
    m.category=item.category or "misc"; m.tags=",".join(item.tags or [])
    m.condition=item.condition or "new"
    m.marketplace_refs=json.dumps(marketplace_refs) if marketplace_refs else ""
    db.commit(); db.refresh(m)

    # Convert database fields to schema format
    item_data = {
        "id": m.id,
        "sku": m.sku,
        "title": m.title,
        "description": m.description or "",
        "price": m.price,
        "status": m.status,
        "cover_url": m.cover_url or "",
        "gallery": [g for g in m.gallery.split(",") if g] if m.gallery else [],
        "category": m.category or "misc",
        "tags": [t for t in m.tags.split(",") if t] if m.tags else [],
        "condition": m.condition or "new",
        "marketplace_refs": json.loads(m.marketplace_refs) if m.marketplace_refs else None
    }
    return schemas.ItemOut(**item_data)

@app.post("/items/{item_id}/status", dependencies=[Depends(admin_guard)])
def set_status(item_id: int, payload: dict, db: Session = Depends(get_db)):
    status = payload.get("status")
    if not status:
        raise HTTPException(422, "Status is required")
    m = db.query(models.Item).get(item_id)
    if not m:
        raise HTTPException(404, "Item not found")
    if status not in ("available","reserved","sold","hidden"):
        raise HTTPException(400, "Bad status")
    m.status = status
    db.commit(); db.refresh(m)
    return {"ok": True, "status": m.status}

@app.post("/admin/items/bulk-status", dependencies=[Depends(admin_guard)])
def bulk_update_status(payload: dict, db: Session = Depends(get_db)):
    """
    Bulk update item statuses
    Payload: {
        "item_ids": [1, 2, 3],
        "status": "sold"
    }
    """
    item_ids = payload.get("item_ids", [])
    status = payload.get("status", "")

    if not item_ids:
        raise HTTPException(400, "item_ids required")
    if status not in ("available","reserved","sold","hidden"):
        raise HTTPException(400, "Bad status")

    updated = []
    not_found = []

    for item_id in item_ids:
        m = db.query(models.Item).get(item_id)
        if not m:
            not_found.append(item_id)
            continue
        m.status = status
        updated.append({"id": m.id, "sku": m.sku, "title": m.title, "status": m.status})

    db.commit()

    return {
        "ok": True,
        "updated": len(updated),
        "not_found": not_found,
        "items": updated
    }

@app.post("/admin/items/status-by-sku", dependencies=[Depends(admin_guard)])
def update_status_by_sku(payload: dict, db: Session = Depends(get_db)):
    """
    Update item status by SKU
    Payload: {
        "sku": "FB-20251013-001",
        "status": "sold"
    }
    """
    sku = payload.get("sku", "")
    status = payload.get("status", "")

    if not sku:
        raise HTTPException(400, "sku required")
    if status not in ("available","reserved","sold","hidden"):
        raise HTTPException(400, "Bad status")

    m = db.query(models.Item).filter(models.Item.sku == sku).first()
    if not m:
        raise HTTPException(404, f"Item with SKU {sku} not found")

    old_status = m.status
    m.status = status  # type: ignore
    db.commit()
    db.refresh(m)

    return {
        "ok": True,
        "id": m.id,
        "sku": m.sku,
        "title": m.title,
        "old_status": old_status,
        "new_status": m.status
    }

@app.post("/admin/items/status-by-title", dependencies=[Depends(admin_guard)])
def update_status_by_title(payload: dict, db: Session = Depends(get_db)):
    """
    Update item status by title (partial match)
    Payload: {
        "title": "JL Audio",
        "status": "sold",
        "exact": false
    }
    """
    title = payload.get("title", "")
    status = payload.get("status", "")
    exact = payload.get("exact", False)

    if not title:
        raise HTTPException(400, "title required")
    if status not in ("available","reserved","sold","hidden"):
        raise HTTPException(400, "Bad status")

    if exact:
        items = db.query(models.Item).filter(models.Item.title == title).all()
    else:
        items = db.query(models.Item).filter(models.Item.title.contains(title)).all()

    if not items:
        raise HTTPException(404, f"No items found matching title: {title}")

    updated = []
    for m in items:
        old_status = m.status
        m.status = status  # type: ignore
        updated.append({
            "id": m.id,
            "sku": m.sku,
            "title": m.title,
            "old_status": old_status,
            "new_status": status
        })

    db.commit()

    return {
        "ok": True,
        "updated": len(updated),
        "items": updated
    }

@app.get("/admin/items/stats", dependencies=[Depends(admin_guard)])
def get_item_stats(db: Session = Depends(get_db)):
    """Get item statistics by status"""
    all_items = db.query(models.Item).all()

    stats = {
        "total": len(all_items),
        "available": 0,
        "reserved": 0,
        "sold": 0,
        "hidden": 0
    }

    for item in all_items:
        if item.status in stats:  # type: ignore
            stats[item.status] += 1  # type: ignore

    return stats

@app.delete("/items/{item_id}", dependencies=[Depends(admin_guard)])
def delete_item(item_id: int, db: Session = Depends(get_db)):
    m = db.query(models.Item).get(item_id)
    if not m:
        raise HTTPException(404, "Item not found")
    db.delete(m)
    db.commit()
    return {"ok": True}

# Bundles
bundles_db = []  # In-memory storage for bundles (in production, use database)

@app.post("/bundles", dependencies=[Depends(admin_guard)])
def create_bundle(bundle: dict, db: Session = Depends(get_db)):
    bundle_id = len(bundles_db) + 1
    products = []
    for pid in bundle.get("product_ids", []):
        item = db.query(models.Item).get(pid)
        if item:
            products.append({
                "id": item.id,
                "title": item.title,
                "price": item.price,
                "cover_url": item.cover_url
            })

    new_bundle = {
        "id": bundle_id,
        "title": bundle["title"],
        "description": bundle.get("description", ""),
        "product_ids": bundle.get("product_ids", []),
        "products": products,
        "total_price": sum(p["price"] for p in products)
    }
    bundles_db.append(new_bundle)
    return new_bundle

@app.get("/bundles")
def list_bundles():
    return bundles_db

@app.delete("/bundles/{bundle_id}", dependencies=[Depends(admin_guard)])
def delete_bundle(bundle_id: int):
    global bundles_db
    bundles_db = [b for b in bundles_db if b["id"] != bundle_id]
    return {"ok": True}

# Orders / Checkout (Stripe optional). We always reserve quantity=1.
@app.post("/checkout", response_model=schemas.OrderOut)
def checkout(payload: schemas.OrderIn, db: Session = Depends(get_db)):
    item = db.query(models.Item).get(payload.item_id)
    if not item or item.status != "available":
        raise HTTPException(409, "Item not available")
    # reserve
    item.status = "reserved"
    order = models.Order(item_id=item.id, email=payload.email, name=payload.name, phone=payload.phone, amount=item.price, status="pending")
    db.add(order)
    db.commit(); db.refresh(order); db.refresh(item)

    client_secret = None
    if STRIPE_KEY:
        intent = stripe.PaymentIntent.create(
            amount=int(item.price * 100),
            currency=os.getenv("STORE_CURRENCY","usd"),
            metadata={"item_id": str(item.id), "order_id": str(order.id), "sku": item.sku},
            automatic_payment_methods={"enabled": True},
        )
        order.stripe_pi = intent.id  # type: ignore
        db.commit()
        client_secret = intent.client_secret

    return {"id": order.id, "item_id": item.id, "amount": item.price, "status": order.status, "client_secret": client_secret}  # type: ignore

@app.post("/stripe/webhook")
async def stripe_webhook(payload: dict):
    # NOTE: In production verify signature; here we trust Docker network/Webhook Secret would be added as validation.
    # When payment succeeds: mark order paid, item sold.
    event_type = payload.get("type")
    data = payload.get("data", {}).get("object", {})
    if event_type == "payment_intent.succeeded":
        order_id = int(data.get("metadata", {}).get("order_id", 0))
        from .db import SessionLocal
        db = SessionLocal()
        try:
            order = db.query(models.Order).get(order_id)
            if order:
                order.status = "paid"
                item = db.query(models.Item).get(order.item_id)
                if item:
                    item.status = "sold"
                db.commit()
        finally:
            db.close()
    return {"received": True}

# PayPal Checkout
@app.post("/checkout/paypal")
def paypal_checkout(payload: schemas.OrderIn, db: Session = Depends(get_db)):
    item = db.query(models.Item).get(payload.item_id)
    if not item or item.status != "available":
        raise HTTPException(409, "Item not available")

    if not PAYPAL_CLIENT_ID:
        raise HTTPException(503, "PayPal not configured")

    # Create PayPal payment
    payment = paypalrestsdk.Payment({
        "intent": "sale",
        "payer": {
            "payment_method": "paypal"
        },
        "redirect_urls": {
            "return_url": f"{os.getenv('FRONTEND_URL', 'http://localhost:5173')}/success",
            "cancel_url": f"{os.getenv('FRONTEND_URL', 'http://localhost:5173')}/cancel"
        },
        "transactions": [{
            "item_list": {
                "items": [{
                    "name": item.title,
                    "sku": item.sku or str(item.id),
                    "price": f"{item.price:.2f}",
                    "currency": os.getenv("STORE_CURRENCY", "USD").upper(),
                    "quantity": 1
                }]
            },
            "amount": {
                "total": f"{item.price:.2f}",
                "currency": os.getenv("STORE_CURRENCY", "USD").upper()
            },
            "description": f"Purchase of {item.title}"
        }]
    })

    if payment.create():
        # Reserve the item
        item.status = "reserved"
        order = models.Order(
            item_id=item.id,
            email=payload.email,
            name=payload.name,
            phone=payload.phone,
            amount=item.price,
            status="pending"
        )
        db.add(order)
        db.commit()
        db.refresh(order)

        # Store PayPal payment ID in order metadata
        payment_metadata = json.loads(order.stripe_pi or "{}")  # type: ignore
        payment_metadata["paypal_payment_id"] = payment.id
        order.stripe_pi = json.dumps(payment_metadata)  # type: ignore
        db.commit()

        return {
            "paypal_url": payment.links[1].href,  # approval_url
            "payment_id": payment.id,
            "order_id": order.id
        }
    else:
        raise HTTPException(500, f"PayPal payment creation failed: {payment.error}")

@app.post("/paypal/webhook")
async def paypal_webhook(payload: dict):
    # Handle PayPal webhook for payment completion
    event_type = payload.get("event_type")
    resource = payload.get("resource", {})

    if event_type == "PAYMENT.SALE.COMPLETED":
        payment_id = resource.get("parent_payment")
        # Find order by PayPal payment ID
        from .db import SessionLocal
        db = SessionLocal()
        try:
            # This is simplified - in production you'd store PayPal payment ID separately
            orders = db.query(models.Order).filter(models.Order.stripe_pi.contains(payment_id)).all()
            for order in orders:
                if order.status == "pending":  # type: ignore
                    order.status = "paid"  # type: ignore
                    item = db.query(models.Item).get(order.item_id)
                    if item:
                        item.status = "sold"  # type: ignore
                    db.commit()
                    break
        finally:
            db.close()

    return {"received": True}

# Categories Management
@app.get("/categories")
def list_categories(db: Session = Depends(get_db)):
    """Get all categories with item counts and sample images"""
    items = db.query(models.Item).filter(models.Item.status == "available").all()
    category_data = {}

    for item in items:
        cat = item.category or "Uncategorized"
        if cat not in category_data:
            category_data[cat] = {
                "name": cat,
                "count": 0,
                "image_url": "",
                "total_value": 0.0
            }

        category_data[cat]["count"] += 1
        category_data[cat]["total_value"] += item.price

        # Use first item with an image as category image
        if not category_data[cat]["image_url"] and item.cover_url:
            category_data[cat]["image_url"] = item.cover_url

    categories = sorted(category_data.values(), key=lambda x: x["count"], reverse=True)

    return {"categories": categories}

@app.get("/categories/{category_name}/items")
def get_category_items(category_name: str, db: Session = Depends(get_db)):
    """Get all items in a specific category"""
    items = db.query(models.Item).filter(
        models.Item.category == category_name,
        models.Item.status == "available"
    ).order_by(models.Item.created_at.desc()).all()

    out = []
    for r in items:
        item_data = {
            "id": r.id,
            "sku": r.sku,
            "title": r.title,
            "description": r.description or "",
            "price": r.price,
            "status": r.status,
            "cover_url": r.cover_url or "",
            "gallery": [g for g in r.gallery.split(",") if g] if r.gallery else [],
            "category": r.category or "misc",
            "tags": [t for t in r.tags.split(",") if t] if r.tags else [],
            "condition": r.condition or "new",
            "marketplace_refs": json.loads(r.marketplace_refs) if r.marketplace_refs else None
        }
        o = schemas.ItemOut(**item_data)
        out.append(o)

    return {"category": category_name, "count": len(out), "items": out}

@app.post("/categories", dependencies=[Depends(admin_guard)])
def create_category(payload: dict, db: Session = Depends(get_db)):
    """Create a new category by adding an item with that category"""
    name = payload.get("name", "").strip()
    if not name:
        raise HTTPException(422, "Category name is required")

    # Check if category already exists
    existing = db.query(models.Item).filter(models.Item.category == name).first()
    if existing:
        raise HTTPException(400, f"Category '{name}' already exists")

    # Create a placeholder item for this category (this is a simple approach)
    # In a real app, you might have a separate categories table
    placeholder_item = models.Item(
        title=f"Category: {name}",
        description=f"Placeholder for category {name}",
        price=0.0,
        category=name,
        status="hidden",  # Hidden placeholder
        sku=f"CAT-{name.upper()}-PLACEHOLDER"
    )

    db.add(placeholder_item)
    db.commit()
    db.refresh(placeholder_item)

    return {"ok": True, "category": name, "id": placeholder_item.id}

@app.post("/admin/items/assign-category", dependencies=[Depends(admin_guard)])
def assign_category_to_items(payload: dict, db: Session = Depends(get_db)):
    """
    Assign category to multiple items
    Payload: {
        "item_ids": [1, 2, 3],
        "category": "Electronics"
    }
    """
    item_ids = payload.get("item_ids", [])
    category = payload.get("category", "")

    if not item_ids:
        raise HTTPException(400, "item_ids required")
    if not category:
        raise HTTPException(400, "category required")
    if len(category) > 64:
        raise HTTPException(400, "Category name too long (max 64 chars)")

    updated = []
    not_found = []

    for item_id in item_ids:
        m = db.query(models.Item).get(item_id)
        if not m:
            not_found.append(item_id)
            continue
        old_category = m.category
        m.category = category
        updated.append({
            "id": m.id,
            "sku": m.sku,
            "title": m.title[:50],
            "old_category": old_category,
            "new_category": category
        })

    db.commit()

    return {
        "ok": True,
        "updated": len(updated),
        "not_found": not_found,
        "items": updated
    }

@app.post("/admin/categories/rename", dependencies=[Depends(admin_guard)])
def rename_category(payload: dict, db: Session = Depends(get_db)):
    """
    Rename a category across all items
    Payload: {
        "old_name": "Electronics",
        "new_name": "Tech & Electronics"
    }
    """
    old_name = payload.get("old_name", "")
    new_name = payload.get("new_name", "")

    if not old_name or not new_name:
        raise HTTPException(400, "old_name and new_name required")

    if len(new_name) > 64:
        raise HTTPException(400, "Category name too long (max 64 chars)")

    # Find all items with old category
    items = db.query(models.Item).filter(models.Item.category == old_name).all()

    if not items:
        raise HTTPException(404, f"No items found with category: {old_name}")

    # Update all items
    updated = []
    for item in items:
        item.category = new_name
        updated.append({"id": item.id, "sku": item.sku, "title": item.title[:50]})

    db.commit()

    return {
        "ok": True,
        "updated": len(updated),
        "old_name": old_name,
        "new_name": new_name,
        "items": updated[:10]  # Show first 10
    }

@app.post("/admin/categories/merge", dependencies=[Depends(admin_guard)])
def merge_categories(payload: dict, db: Session = Depends(get_db)):
    """
    Merge multiple categories into one
    Payload: {
        "source_categories": ["Electronics", "Tech", "Gadgets"],
        "target_category": "Electronics"
    }
    """
    source_cats = payload.get("source_categories", [])
    target_cat = payload.get("target_category", "")

    if not source_cats or not target_cat:
        raise HTTPException(400, "source_categories and target_category required")

    if len(target_cat) > 64:
        raise HTTPException(400, "Category name too long (max 64 chars)")

    # Find all items with source categories
    items = db.query(models.Item).filter(models.Item.category.in_(source_cats)).all()

    if not items:
        raise HTTPException(404, f"No items found with categories: {source_cats}")

    # Update all items
    updated_count = 0
    for item in items:
        old_cat = item.category
        item.category = target_cat
        updated_count += 1

    db.commit()

    return {
        "ok": True,
        "updated": updated_count,
        "merged_from": source_cats,
        "merged_to": target_cat
    }

@app.delete("/admin/categories/{category_name}", dependencies=[Depends(admin_guard)])
def delete_category(category_name: str, db: Session = Depends(get_db)):
    """Delete a category by moving all items to 'Uncategorized'"""
    items_with_category = db.query(models.Item).filter(models.Item.category == category_name).all()

    if not items_with_category:
        return {"ok": True, "message": "Category not found or already empty"}

    # Move items to Uncategorized
    for item in items_with_category:
        item.category = "Uncategorized"  # type: ignore

    db.commit()

    return {
        "ok": True,
        "moved_items": len(items_with_category),
        "message": f"Moved {len(items_with_category)} items to 'Uncategorized'"
    }

# File Uploads
@app.post("/upload/images", dependencies=[Depends(admin_guard)])
async def upload_images(files: list[UploadFile] = File(...)):
    if len(files) > 10:
        raise HTTPException(400, "Maximum 10 images allowed")
    
    uploaded = []
    for file in files:
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(400, f"File {file.filename} is not an image")
        
        if not file.filename:
            raise HTTPException(400, "Filename is required")
        
        # Generate unique filename
        ext = Path(file.filename).suffix.lower()
        if ext not in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
            raise HTTPException(400, f"Unsupported image format: {ext}")
        
        filename = f"{int(time.time()*1000)}_{uuid.uuid4().hex[:8]}{ext}"
        file_path = UPLOAD_DIR / filename
        
        with file_path.open("wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        uploaded.append({
            "filename": filename,
            "original_name": file.filename,
            "url": f"/uploads/{filename}"
        })
    
    return {"uploaded": uploaded}

@app.post("/upload/video", dependencies=[Depends(admin_guard)])
async def upload_video(file: UploadFile = File(...)):
    if not file.content_type or not file.content_type.startswith('video/'):
        raise HTTPException(400, "File is not a video")
    
    if not file.filename:
        raise HTTPException(400, "Filename is required")
    
    # Check file size (limit to ~100MB for 1 minute video)
    file_size = 0
    content = await file.read()
    file_size = len(content)
    
    if file_size > 100 * 1024 * 1024:  # 100MB
        raise HTTPException(400, "Video file too large (max 100MB)")
    
    # Generate unique filename
    ext = Path(file.filename).suffix.lower()
    if ext not in ['.mp4', '.mov', '.avi', '.webm']:
        raise HTTPException(400, f"Unsupported video format: {ext}")
    
    filename = f"video_{int(time.time()*1000)}_{uuid.uuid4().hex[:8]}{ext}"
    file_path = UPLOAD_DIR / filename
    
    with file_path.open("wb") as buffer:
        buffer.write(content)
    
    return {
        "filename": filename,
        "original_name": file.filename,
        "url": f"/uploads/{filename}",
        "size": file_size
    }

# Serve uploaded files
@app.get("/uploads/{filename}")
async def get_uploaded_file(filename: str):
    file_path = UPLOAD_DIR / filename
    if not file_path.exists():
        raise HTTPException(404, "File not found")
    return FileResponse(file_path)

# Marketplace Credentials Status
@app.get("/admin/marketplace-credentials", dependencies=[Depends(admin_guard)])
def get_marketplace_credentials():
    """Get status of marketplace credentials configuration"""
    def mask_credential(value: str) -> str:
        if not value or len(value) < 4:
            return "***"
        return value[:2] + "***" + value[-2:]

    credentials = {
        "facebook": {
            "available": bool(os.getenv("FACEBOOK_EMAIL") and os.getenv("FACEBOOK_PASSWORD")),
            "email": mask_credential(os.getenv("FACEBOOK_EMAIL", "")) if os.getenv("FACEBOOK_EMAIL") else ""
        },
        "ebay": {
            "available": bool(os.getenv("EBAY_USERNAME") and os.getenv("EBAY_PASSWORD")),
            "username": mask_credential(os.getenv("EBAY_USERNAME", "")) if os.getenv("EBAY_USERNAME") else ""
        },
        "craigslist": {
            "available": bool(os.getenv("CRAIGSLIST_EMAIL") and os.getenv("CRAIGSLIST_PASSWORD")),
            "email": mask_credential(os.getenv("CRAIGSLIST_EMAIL", "")) if os.getenv("CRAIGSLIST_EMAIL") else ""
        }
    }

    return {
        "credentials": credentials,
        "total_available": sum(1 for cred in credentials.values() if cred["available"])
    }

# LLM Enhancement
from .services.llm_enhance import LLMEnhancer

@app.post("/admin/enhance-description", dependencies=[Depends(admin_guard)])
async def enhance_description(payload: dict):
    """
    Enhance product description using AI
    Payload: {
        "title": "Product title",
        "description": "Original description",
        "price": 99.99,
        "category": "Electronics",
        "condition": "used",
        "model": "deepseek"  # optional: deepseek, mistral, llama, qwen
    }
    """
    enhancer = LLMEnhancer()
    
    result = await enhancer.enhance_description(
        title=payload.get("title", ""),
        original_description=payload.get("description", ""),
        price=payload.get("price"),
        category=payload.get("category", "Other"),
        condition=payload.get("condition", "used"),
        model=payload.get("model", "deepseek")
    )
    
    return result

# Cross-platform Management
@app.get("/admin/marketplace-stats", dependencies=[Depends(admin_guard)])
def get_marketplace_stats(db: Session = Depends(get_db)):
    """Get statistics about items cross-posted to marketplaces"""
    items = db.query(models.Item).all()
    
    stats = {
        "total_items": len(items),
        "facebook_listings": 0,
        "ebay_listings": 0,
        "craigslist_listings": 0,
        "multi_platform": 0
    }
    
    for item in items:
        refs = json.loads(getattr(item, "marketplace_refs", "{}") or "{}")
        platforms = []
        
        if refs.get("facebook_id"):
            stats["facebook_listings"] += 1
            platforms.append("facebook")
        if refs.get("ebay_id"):
            stats["ebay_listings"] += 1
            platforms.append("ebay")
        if refs.get("craigslist_id"):
            stats["craigslist_listings"] += 1
            platforms.append("craigslist")
        
        if len(platforms) > 1:
            stats["multi_platform"] += 1
    
    return stats

@app.post("/admin/cross-post/ebay", dependencies=[Depends(admin_guard)])
async def cross_post_to_ebay(payload: dict, db: Session = Depends(get_db)):
    """
    Cross-post item to eBay
    Payload: {
        "item_id": 123,
        "ebay_category_id": "9355",  # eBay category ID
        "shipping_cost": 0,
        "duration_days": 7
    }
    """
    item_id = payload.get("item_id")
    if not item_id:
        raise HTTPException(400, "item_id required")
    
    item = db.query(models.Item).get(item_id)
    if not item:
        raise HTTPException(404, "Item not found")
    
    # Check eBay limit (250 active listings)
    items_with_ebay = db.query(models.Item).filter(
        models.Item.marketplace_refs.contains('"ebay_id"')
    ).all()
    
    active_ebay_count = sum(1 for i in items_with_ebay if getattr(i, "status", None) == "available")
    
    if active_ebay_count >= 250:
        return {
            "ok": False,
            "error": "eBay limit reached (250 active listings)",
            "current_count": active_ebay_count,
            "suggestion": "Mark some items as sold or remove from eBay to free up slots"
        }
    
    # TODO: Actual eBay API integration
    # For now, just simulate by updating marketplace_refs
    refs = json.loads(item.marketplace_refs) if item.marketplace_refs else {}
    refs["ebay_id"] = f"ebay_{int(time.time())}"
    refs["ebay_posted_at"] = time.time()
    item.marketplace_refs = json.dumps(refs)
    db.commit()
    
    return {
        "ok": True,
        "item_id": item.id,
        "ebay_id": refs["ebay_id"],
        "active_ebay_count": active_ebay_count + 1,
        "slots_remaining": 249 - active_ebay_count
    }

@app.post("/admin/cross-post/craigslist", dependencies=[Depends(admin_guard)])
async def cross_post_to_craigslist(payload: dict, db: Session = Depends(get_db)):
    """
    Cross-post item to Craigslist
    Payload: {
        "item_id": 123,
        "location": "Chicago",
        "area": "nwi"  # Craigslist area code
    }
    """
    item_id = payload.get("item_id")
    if not item_id:
        raise HTTPException(400, "item_id required")
    
    item = db.query(models.Item).get(item_id)
    if not item:
        raise HTTPException(404, "Item not found")
    
    # TODO: Actual Craigslist API/automation integration
    # For now, simulate
    refs = json.loads(item.marketplace_refs) if item.marketplace_refs else {}
    refs["craigslist_id"] = f"cl_{int(time.time())}"
    refs["craigslist_posted_at"] = time.time()
    refs["craigslist_location"] = payload.get("location", "")
    item.marketplace_refs = json.dumps(refs)
    db.commit()
    
    return {
        "ok": True,
        "item_id": item.id,
        "craigslist_id": refs["craigslist_id"]
    }

@app.post("/admin/sync-sold-item", dependencies=[Depends(admin_guard)])
async def sync_sold_item(payload: dict, db: Session = Depends(get_db)):
    """
    Mark item as sold across ALL platforms
    Removes from Facebook, eBay, Craigslist, archives in OneOf Store
    
    Payload: {
        "item_id": 123,
        "sold_on": "ebay",  # which platform it sold on
        "sale_price": 99.99  # optional
    }
    """
    item_id = payload.get("item_id")
    if not item_id:
        raise HTTPException(400, "item_id required")
    
    item = db.query(models.Item).get(item_id)
    if not item:
        raise HTTPException(404, "Item not found")
    
    sold_on = payload.get("sold_on", "unknown")
    sale_price = payload.get("sale_price", item.price)
    
    # Get marketplace references
    refs = json.loads(item.marketplace_refs) if item.marketplace_refs else {}
    
    platforms_to_remove = []
    
    # TODO: Actually remove from each platform
    if refs.get("facebook_id"):
        platforms_to_remove.append("Facebook Marketplace")
        # API call to remove Facebook listing
    
    if refs.get("ebay_id"):
        platforms_to_remove.append("eBay")
        # API call to end eBay listing
    
    if refs.get("craigslist_id"):
        platforms_to_remove.append("Craigslist")
        # API call to remove Craigslist posting
    
    # Mark as sold in OneOf Store
    item.status = "sold"
    
    # Update marketplace refs with sold info
    refs["sold_on"] = sold_on
    refs["sold_at"] = time.time()
    refs["sale_price"] = sale_price
    item.marketplace_refs = json.dumps(refs)
    
    db.commit()
    
    return {
        "ok": True,
        "item_id": item.id,
        "sku": item.sku,
        "title": item.title,
        "sold_on": sold_on,
        "sale_price": sale_price,
        "removed_from": platforms_to_remove,
        "message": f"Item removed from {len(platforms_to_remove)} platforms and archived"
    }
