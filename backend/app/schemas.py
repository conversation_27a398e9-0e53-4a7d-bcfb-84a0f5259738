from pydantic import BaseModel, Field
from typing import Optional, List, Dict

class ItemIn(BaseModel):
    sku: Optional[str] = None
    title: str
    description: Optional[str] = ""
    price: float
    cover_url: Optional[str] = ""
    gallery: Optional[List[str]] = []
    category: Optional[str] = "misc"
    tags: Optional[List[str]] = []
    condition: Optional[str] = "new"

class ItemOut(ItemIn):
    id: int
    status: str
    marketplace_refs: Dict[str, str] | None = None

    class Config:
        from_attributes = True

class OrderIn(BaseModel):
    item_id: int
    email: str
    name: str
    phone: str

class OrderOut(BaseModel):
    id: int
    item_id: int
    amount: float
    status: str
    client_secret: Optional[str] = None

    class Config:
        from_attributes = True
