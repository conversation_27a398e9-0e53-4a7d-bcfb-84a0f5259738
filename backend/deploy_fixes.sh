#!/bin/bash
# Deploy backend fixes to production server

set -e

# Configuration
SERVER="root@104.237.9.52"
REMOTE_PATH="/var/www/oneofstore/backend"

echo "🚀 Deploying backend fixes to production server..."
echo "   Server: $SERVER"
echo "   Remote path: $REMOTE_PATH"
echo ""

# Create backup of current files on server
echo "💾 Creating backup of current files..."
ssh "$SERVER" "cd $REMOTE_PATH && \
    mkdir -p backups/$(date +%Y%m%d_%H%M%S) && \
    cp app/main.py backups/$(date +%Y%m%d_%H%M%S)/ && \
    cp app/schemas.py backups/$(date +%Y%m%d_%H%M%S)/ && \
    echo 'Backup created in backups/$(date +%Y%m%d_%H%M%S)/'"

# Upload fixed files
echo "📤 Uploading fixed backend files..."
scp app/main.py "$SERVER:$REMOTE_PATH/app/"
scp app/schemas.py "$SERVER:$REMOTE_PATH/app/"

# Restart the backend service
echo "🔄 Restarting backend service..."
ssh "$SERVER" "cd /var/www/oneofstore && \
    echo '--- Stopping backend ---' && \
    (docker compose stop backend || systemctl stop oneofstore-backend || echo 'Could not stop backend') && \
    echo '--- Starting backend ---' && \
    (docker compose start backend || systemctl start oneofstore-backend || echo 'Could not start backend') && \
    echo '--- Checking status ---' && \
    sleep 5 && \
    curl -s http://localhost:8000/health || echo 'Health check failed'"

echo ""
echo "✅ Deployment complete!"
echo ""
echo "🧪 Testing the fixes..."
echo "Testing API endpoint..."

# Test the API
echo "📡 Testing API with fb_url field..."
curl -X POST https://oneofstore.com/api/items \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17" \
  -H "Content-Type: application/json" \
  -d '{
    "sku": "TEST-DEPLOY-001",
    "title": "Test Deploy Item",
    "price": 1.00,
    "description": "Test deployment with fb_url field",
    "cover_url": "https://example.com/test.jpg",
    "fb_url": "https://facebook.com/marketplace/item/deploy-test"
  }' || echo "❌ API test failed"

echo ""
echo "🎉 Deployment and testing complete!"
echo ""
echo "Your remote server should now have the same fixes as your local server:"
echo "  ✅ fb_url field support in ItemIn schema"
echo "  ✅ Fixed create_item endpoint with proper field conversion"
echo "  ✅ Fixed update_item endpoint with proper field conversion"
echo ""
echo "You can now use https://oneofstore.com/api in your Chrome extension!"
