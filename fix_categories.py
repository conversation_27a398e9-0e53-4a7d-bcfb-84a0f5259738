#!/usr/bin/env python3
"""Fix category issues"""

import requests
import json

API = "https://oneofstore.com/api"
TOKEN = "changeme-super-secret-admin-api-bearer-moskva17"
HEADERS = {"Authorization": f"Bearer {TOKEN}", "Content-Type": "application/json"}

def main():
    # Get all items
    print("Fetching items...")
    items = requests.get(f"{API}/items").json()
    print(f"Total items: {len(items)}")

    # 1. Create Watches category
    print("\n1. Creating 'Watches' category...")
    try:
        r = requests.post(f"{API}/categories", headers=HEADERS, json={"name": "Watches"})
        print(f"   {r.json()}")
    except:
        print("   Watches category may already exist")

    # 2. Split Jewelry and Watches
    print("\n2. Splitting 'Jewelry and Watches' into 'Jewelry' and 'Watches'...")
    jw_items = [i for i in items if i.get('category') == 'Jewelry and Watches']

    watches = []
    jewelry = []

    for item in jw_items:
        title = item['title'].lower()
        desc = (item.get('description') or '').lower()

        if 'watch' in title or 'clock' in title or 'watch' in desc:
            watches.append(item['id'])
        else:
            jewelry.append(item['id'])

    print(f"   Found {len(watches)} watches, {len(jewelry)} jewelry items")

    # Move watches to Watches category
    if watches:
        print(f"   Moving {len(watches)} items to 'Watches' category...")
        r = requests.post(f"{API}/admin/items/assign-category",
                         headers=HEADERS,
                         json={"item_ids": watches, "category": "Watches"})
        print(f"   Result: {r.json().get('updated', 0)} items updated")

    # Rename "Jewelry and Watches" to just "Jewelry"
    print("\n3. Renaming 'Jewelry and Watches' to 'Jewelry'...")
    try:
        r = requests.post(f"{API}/admin/categories/rename",
                         headers=HEADERS,
                         json={"old_name": "Jewelry and Watches", "new_name": "Jewelry"})
        print(f"   {r.json()}")
    except Exception as e:
        print(f"   Error: {e}")

    # 4. Find RC cars and drones
    print("\n4. Finding RC cars and drones...")
    rc_keywords = ['rc ', ' rc', 'remote control', 'drone', 'quadcopter', 'helicopter remote']
    rc_items = []

    for item in items:
        title = item['title'].lower()
        desc = (item.get('description') or '').lower()

        if any(kw in title or kw in desc for kw in rc_keywords):
            rc_items.append({
                'id': item['id'],
                'title': item['title'][:60],
                'category': item.get('category')
            })

    print(f"   Found {len(rc_items)} RC/drone items:")
    for rc in rc_items[:10]:
        print(f"     [{rc['id']}] {rc['title']} (current: {rc['category']})")

    if rc_items:
        rc_ids = [rc['id'] for rc in rc_items]
        print(f"\n   Moving {len(rc_ids)} RC/drone items to 'Toys' category...")
        r = requests.post(f"{API}/admin/items/assign-category",
                         headers=HEADERS,
                         json={"item_ids": rc_ids, "category": "Toys"})
        print(f"   Result: {r.json().get('updated', 0)} items updated")

    # 5. Create Children's Books category
    print("\n5. Creating 'Children's Books' category...")
    try:
        r = requests.post(f"{API}/categories", headers=HEADERS, json={"name": "Children's Books"})
        print(f"   {r.json()}")
    except:
        print("   Children's Books category may already exist")

    # Find children's books
    childrens_keywords = ['baby einstein', 'disney', 'sesame street', 'kids', 'children']
    childrens_books = []

    book_items = [i for i in items if i.get('category') == 'Books']
    for item in book_items:
        title = item['title'].lower()
        desc = (item.get('description') or '').lower()

        if any(kw in title or kw in desc for kw in childrens_keywords):
            childrens_books.append(item['id'])

    if childrens_books:
        print(f"   Moving {len(childrens_books)} children's books...")
        r = requests.post(f"{API}/admin/items/assign-category",
                         headers=HEADERS,
                         json={"item_ids": childrens_books, "category": "Children's Books"})
        print(f"   Result: {r.json().get('updated', 0)} items updated")

    print("\n✅ Done!")

if __name__ == "__main__":
    main()
