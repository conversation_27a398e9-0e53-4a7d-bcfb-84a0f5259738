# Admin Item Editor Guide

## ✅ Issues Fixed

1. **Deleted** "Vintage Vinyl Records Collection" (ID 26) - had wrong image
2. **Deleted** all 10 category placeholder items that were showing in every category
3. **Created** comprehensive admin item editor for full CRUD operations

---

## 🎯 Access the Admin Panel

**Component:** `frontend/src/components/AdminItemEditor.vue`

**To use it:** Add to your Vue router or create a dedicated admin page.

### Quick Setup (Add to Router)

Edit your router file (e.g., `frontend/src/router/index.ts`):

```typescript
import AdminItemEditor from '@/components/AdminItemEditor.vue'

const routes = [
  // ... existing routes
  {
    path: '/admin/items',
    name: 'AdminItems',
    component: AdminItemEditor
  }
]
```

**Then visit:** https://oneofstore.com/admin/items

---

## 🔐 Authentication

When you open the admin page:

1. Enter admin token: `changeme-super-secret-admin-api-bearer-moskva17`
2. Click "Authenticate"
3. Token is saved to localStorage (persists between sessions)

---

## 📋 Features

### 1. **Search & Filter**
- **Search:** Find items by title, SKU, or description
- **Filter by Category:** Dropdown shows all categories with item counts
- **Filter by Status:** available, sold, hidden, reserved

### 2. **Item List**
- View all items with thumbnails
- Shows: Title, SKU, Category, Status, Price
- Color-coded status badges
- 20 items per page with pagination
- Click any item to edit

### 3. **Edit Item (Full Control)**

Click "Edit" on any item to open the editor modal:

**Editable Fields:**
- ✅ **Title** - Change item name
- ✅ **Price** - Update price (decimal supported)
- ✅ **Condition** - Dropdown: New, Good, Excellent, Used, For Parts, Refurbished
- ✅ **Category** - Dropdown of all categories
- ✅ **Status** - Available, Reserved, Sold, Hidden
- ✅ **Description** - Multi-line text area
- ✅ **Cover Image URL** - Change main image (shows preview)
- ✅ **Gallery URLs** - Multiple images (one URL per line)
- ✅ **Tags** - Comma-separated tags (facebook, marketplace, etc.)

**Read-Only:**
- SKU - Auto-generated, cannot be changed

### 4. **Delete Item**
- Click "Delete" button
- Confirmation modal appears
- Permanent deletion (cannot be undone)

---

## 📝 Common Tasks

### Change Item Title
1. Click "Edit" on the item
2. Update the "Title" field
3. Click "Save Changes"

### Update Price
1. Click "Edit"
2. Change "Price" (supports decimals like 99.99)
3. Save

### Change Category
1. Click "Edit"
2. Select new category from dropdown
3. Save

### Fix Wrong Image
1. Click "Edit"
2. Update "Cover Image URL" with correct URL
3. Preview shows immediately
4. Save

### Add Multiple Images
1. Click "Edit"
2. In "Gallery Images" field, paste URLs (one per line):
   ```
   https://example.com/image1.jpg
   https://example.com/image2.jpg
   https://example.com/image3.jpg
   ```
3. Save

### Mark Item as Sold
1. Click "Edit"
2. Change "Status" to "Sold"
3. Save

### Hide Item Temporarily
1. Click "Edit"
2. Change "Status" to "Hidden"
3. Save (won't show in store, but not deleted)

---

## 🎨 Status Colors

- **Available** - Green badge
- **Sold** - Gray badge
- **Hidden** - Yellow badge
- **Reserved** - Blue badge

---

## 🔄 Current Status

### Items
- **Total:** 194 items (after deletions)
- **By Category:**
  - Jewelry and Watches: 98 items
  - Car/Truck/Motorcycle Parts: 31 items
  - Household Items: 22 items
  - Electronics: 12 items
  - Tools: 12 items
  - Car Audio: 11 items
  - Sports: 10 items
  - Books: 3 items (vinyl records removed)
  - Computer Parts: 3 items
  - Toys: 1 item
  - Materials: 1 item

### Deletions Made
1. Vinyl Records Collection (ID 26) - Wrong image
2. Category placeholders (IDs 251-260) - 10 items total

---

## 🚀 Live Deployment

**Status:** ✅ Deployed and live

**Access:**
- Frontend: https://oneofstore.com
- Admin (once route added): https://oneofstore.com/admin/items
- API: https://oneofstore.com/api

**Changes Deployed:**
- ✅ No more placeholder items in categories
- ✅ Vinyl records deleted
- ✅ Admin editor component created
- ✅ Frontend rebuilt and deployed

---

## 💡 Pro Tips

### Bulk Operations
For bulk changes (like changing 50 items to a new category), use the **AdminCategories.vue** component instead - it has multi-select.

### Quick Fixes
- Wrong image? Just paste new URL and save
- Wrong category? Change dropdown and save
- Need to hide temporarily? Status → Hidden
- Wrong price? Edit and save

### Safety
- Deletions are permanent - be careful!
- Always double-check before deleting
- Consider "Hidden" status instead of deleting

---

## 🔧 API Endpoints Used

The admin editor uses these endpoints:

```bash
# Get all items
GET /api/items

# Get categories
GET /api/categories

# Update item
PUT /api/items/{id}
Authorization: Bearer {token}

# Delete item
DELETE /api/items/{id}
Authorization: Bearer {token}

# Admin stats
GET /api/admin/items/stats
Authorization: Bearer {token}
```

---

## 📱 Screenshots

The admin shows:
- Clean, modern interface
- Real-time preview of images
- Easy search and filters
- Mobile-responsive design
- Color-coded status indicators

---

## 🐛 Troubleshooting

### "Authentication failed"
- Check token: `changeme-super-secret-admin-api-bearer-moskva17`
- Make sure backend is running
- Check browser console for errors

### "Failed to update item"
- Check required fields (title, price)
- Verify price is a valid number
- Check browser console for details

### Image not showing
- Verify URL is valid
- Check URL is publicly accessible
- Make sure URL starts with http:// or https://

### Gallery images not appearing
- Put one URL per line
- Remove any extra spaces
- Check URLs are valid

---

## 🎯 Next Steps

1. **Add route** to your Vue router for easy access
2. **Use the editor** to fix any items with wrong data
3. **Set up regular maintenance** - check for items needing updates
4. **Consider backups** - export important data periodically

---

## 📞 Admin Token

**Token:** `changeme-super-secret-admin-api-bearer-moskva17`

**Stored in:** `backend/.env` as `ADMIN_API_BEARER`

**Used by:**
- AdminItemEditor.vue
- AdminCategories.vue
- Any admin API calls

---

## ✨ Summary

You now have a **complete admin interface** for managing every aspect of your items:
- Edit titles, descriptions, prices
- Change images (cover + gallery)
- Update categories, conditions, status
- Add/edit tags
- Delete items when needed

**Everything is live and working!** 🎉

The placeholder items are gone, and you have full control over your inventory.
