# Managing Items in OneOf Store

## Item Status Options

Your items can have 4 different statuses:

- **`available`** - Item is visible in the store and can be purchased
- **`reserved`** - Item is temporarily reserved (during checkout)
- **`sold`** - Item has been sold (hidden from store)
- **`hidden`** - Item is hidden from store (for drafts or temporarily unavailable items)

## Re-Enabling Products That Didn't Sell or Were Used for Testing

If you have items that were marked as `sold` or `hidden` and you want to make them available again, here are your options:

### Option 1: Re-enable via API (Bulk Update)

If you have multiple items to re-enable, use the bulk status update endpoint:

```bash
# Get your admin token from chrome-extension storage or use default
ADMIN_TOKEN="changeme-super-secret-admin-api-bearer-moskva17"

# Method 1: Bulk update by IDs (if you know the item IDs)
curl -X POST http://localhost:8025/api/admin/items/bulk-status \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "item_ids": [100, 101, 102, 103],
    "status": "available"
  }'

# Method 2: Update by title (partial match)
# This finds all items containing "test" and makes them available
curl -X POST http://localhost:8025/api/admin/items/status-by-title \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "test",
    "status": "available",
    "exact": false
  }'

# Method 3: Update by SKU
curl -X POST http://localhost:8025/api/admin/items/status-by-sku \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "sku": "FB-123456",
    "status": "available"
  }'
```

### Option 2: Check Inventory Statistics

First, see what you have:

```bash
curl -X GET http://localhost:8025/api/admin/items/stats \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17"
```

This returns:
```json
{
  "total": 233,
  "available": 220,
  "reserved": 3,
  "sold": 8,
  "hidden": 2
}
```

### Option 3: Get All Items (Including Hidden/Sold)

To see ALL items regardless of status and find the ones you want to re-enable:

```bash
# Get all items
curl -X GET http://localhost:8025/api/items \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17"
```

Look through the results and note the `id` or `sku` of items you want to re-enable, then use the bulk update method above.

### Option 4: Python Script to Re-enable All Hidden/Sold Items

Create a script `reenable_items.py`:

```python
import requests

API_URL = "http://localhost:8025/api"
ADMIN_TOKEN = "changeme-super-secret-admin-api-bearer-moskva17"

headers = {
    "Authorization": f"Bearer {ADMIN_TOKEN}",
    "Content-Type": "application/json"
}

# Get all items
response = requests.get(f"{API_URL}/items", headers=headers)
all_items = response.json()

# Find items that are sold or hidden
to_reenable = [item['id'] for item in all_items if item['status'] in ['sold', 'hidden']]

print(f"Found {len(to_reenable)} items to re-enable")

if to_reenable:
    # Re-enable them
    response = requests.post(
        f"{API_URL}/admin/items/bulk-status",
        headers=headers,
        json={"item_ids": to_reenable, "status": "available"}
    )
    result = response.json()
    print(f"Re-enabled {result['updated']} items")
    for item in result['items']:
        print(f"  ✅ {item['title']} (ID: {item['id']})")
else:
    print("No items to re-enable")
```

Run it:
```bash
cd /home/<USER>/Documents/Source/one-of-store/oneof-store/backend
python3 reenable_items.py
```

## Other Common Tasks

### Mark Items as Sold (when you sell on Facebook)

```bash
# By title (partial match)
curl -X POST http://localhost:8025/api/admin/items/status-by-title \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title": "Subwoofer", "status": "sold", "exact": false}'

# By SKU
curl -X POST http://localhost:8025/api/admin/items/status-by-sku \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"sku": "FB-123456", "status": "sold"}'
```

### Hide Items Temporarily

```bash
curl -X POST http://localhost:8025/api/admin/items/bulk-status \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"item_ids": [100, 101], "status": "hidden"}'
```

## Full Admin API Documentation

See `/home/<USER>/Documents/Source/one-of-store/oneof-store/backend/ADMIN_API.md` for complete API reference including:
- Category management (rename, merge, delete)
- Status updates (by ID, SKU, title, bulk)
- Inventory statistics

## Quick Reference

| Task | Command |
|------|---------|
| Check stats | `GET /admin/items/stats` |
| Re-enable item by ID | `POST /items/{id}/status?status=available` |
| Re-enable bulk | `POST /admin/items/bulk-status` with `{"item_ids": [...], "status": "available"}` |
| Mark as sold | `POST /admin/items/status-by-title` with `{"title": "...", "status": "sold"}` |
| Hide item | `POST /items/{id}/status?status=hidden` |

---

## Chrome Extension Image Fix

**NEW EXTRACTION METHOD**: The extension now finds the clickable URL for each listing card and returns the URLs to the popup. The popup then fetches full details (title, description, all images) from each individual listing page.

### How It Works Now

1. **Content script** (`content.js`):
   - Finds all `div[role="button"]` with prices
   - Traverses up the DOM tree to find parent `<a>` tags with `/item/` URLs
   - Returns list of URLs with item IDs

2. **Popup script** (`popup.js`):
   - Receives the URLs from content script
   - For each URL, fetches the HTML directly (using `fetch()`)
   - Parses the HTML to extract title, price, description, and all images
   - This avoids Facebook's lazy-loading issues completely

### Test the Fix

1. Reload extension in `chrome://extensions`
2. Go to Facebook Marketplace → You → Selling
3. Run the extension
4. Check console logs - you should see:
   - "Found X potential listing cards"
   - "1. https://facebook.com/marketplace/item/123456"
   - "2. https://facebook.com/marketplace/item/789012"
   - etc.
5. Popup will fetch full details from each URL
6. Items should now have images and descriptions

### Benefits of New Approach

✅ Gets direct item URLs (not affected by lazy loading)
✅ Fetches full HTML from detail pages (complete data)
✅ Extracts all images, not just thumbnails
✅ Gets full description from listing page
✅ More reliable than trying to extract from card previews
