/**
 * DEBUG SCRIPT - Click listing card to see where it goes
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Test Listing Card Click');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

// Find a listing card with aria-label
console.log('🔍 Finding listing cards...');
const listingCards = Array.from(document.querySelectorAll('[aria-label][role="button"]'))
  .filter(el => {
    const label = el.getAttribute('aria-label');
    return label && label.length > 20 && !label.includes('More options') &&
           !label.includes('Share') && !label.includes('Mark') && !label.includes('Renew');
  });

console.log(`Found ${listingCards.length} potential listing cards\n`);

if (listingCards.length === 0) {
  console.log('❌ No listing cards found');
} else {
  const card = listingCards[0];
  const title = card.getAttribute('aria-label');

  console.log('📦 FIRST LISTING CARD:');
  console.log(`  Title: "${title}"`);
  console.log('');

  // Check if there's an image in this card
  const img = card.querySelector('img');
  if (img) {
    const src = img.src;
    console.log('  Image URL:', src.substring(0, 100) + '...');

    // Extract potential ID from image URL
    const imageIds = src.match(/\d{15,20}/g);
    if (imageIds) {
      console.log('  Potential IDs from image:', imageIds);
    }
  }

  console.log('');
  console.log('='.repeat(80));
  console.log('🧪 TEST: Clicking listing card');
  console.log('='.repeat(80));
  console.log('');
  console.log('⚠️  This will click the listing and navigate/open a modal');
  console.log('⚠️  Watch what happens!');
  console.log('');
  console.log('Starting in 3 seconds...\n');

  setTimeout(() => {
    console.log('📍 Current URL:', window.location.href);
    console.log('');
    console.log('🖱️  Clicking listing card...');

    // Save current URL
    const originalUrl = window.location.href;

    // Scroll into view
    card.scrollIntoView({ behavior: 'smooth', block: 'center' });

    setTimeout(() => {
      // Click the card
      card.click();

      console.log('✅ Clicked!');
      console.log('');
      console.log('⏳ Waiting to see what happens...');

      // Check for URL change or modal after 2 seconds
      setTimeout(() => {
        const newUrl = window.location.href;

        console.log('');
        console.log('='.repeat(80));
        console.log('📊 RESULT');
        console.log('='.repeat(80));

        if (newUrl !== originalUrl) {
          console.log('✅ URL CHANGED!');
          console.log(`  From: ${originalUrl}`);
          console.log(`  To:   ${newUrl}`);
          console.log('');

          // Check if it's a share URL or item URL
          if (newUrl.includes('/share/')) {
            const shareMatch = newUrl.match(/\/share\/([a-zA-Z0-9]+)/);
            console.log('🎉🎉🎉 IT NAVIGATED TO A SHARE URL!');
            console.log(`  Share ID: ${shareMatch ? shareMatch[1] : 'unknown'}`);
            console.log(`  Share URL: ${newUrl.split('?')[0]}`);
            console.log('');
            console.log('✅ SOLUTION: Click listing card → Extract share URL from address bar!');
          } else if (newUrl.includes('/item/')) {
            const itemMatch = newUrl.match(/\/item\/(\d+)/);
            console.log('🎉 IT NAVIGATED TO AN ITEM URL!');
            console.log(`  Item ID: ${itemMatch ? itemMatch[1] : 'unknown'}`);
            console.log(`  Item URL: ${newUrl.split('?')[0]}`);
            console.log('');
            console.log('✅ SOLUTION: Click listing card → Extract item URL → Navigate and extract data!');
          } else if (newUrl.includes('listing_id=')) {
            const listingMatch = newUrl.match(/listing_id=(\d+)/);
            console.log('🎉 IT HAS A LISTING_ID PARAMETER!');
            console.log(`  Listing ID: ${listingMatch ? listingMatch[1] : 'unknown'}`);
            console.log('');
            console.log('✅ SOLUTION: Click listing card → Extract listing_id → Construct share URL!');
          } else {
            console.log('⚠️  URL changed but not to expected format');
            console.log('  Need to investigate this URL structure');
          }

          // Now check if we can find share URL on this page
          console.log('');
          console.log('🔍 Looking for share URL on this page...');

          const shareLinks = document.querySelectorAll('a[href*="/share/"]');
          if (shareLinks.length > 0) {
            console.log(`  ✅ Found ${shareLinks.length} share link(s):`);
            shareLinks.forEach((link, idx) => {
              console.log(`    ${idx + 1}. ${link.href}`);
            });
          } else {
            // Search HTML source
            const html = document.documentElement.outerHTML;
            const shareMatches = html.match(/\/share\/[a-zA-Z0-9]{8,20}/g);
            if (shareMatches) {
              const uniqueUrls = [...new Set(shareMatches)];
              console.log(`  ✅ Found ${uniqueUrls.length} share URL(s) in HTML:`);
              uniqueUrls.forEach((url, idx) => {
                console.log(`    ${idx + 1}. ${url}`);
              });
            } else {
              console.log('  ❌ No share URLs found on this page');
            }
          }

          console.log('');
          console.log('💡 To go back to selling page:');
          console.log(`  window.history.back()`);
          console.log('  Or click the Back button');

        } else {
          // Check if a modal/dialog opened
          const modal = document.querySelector('[role="dialog"]');
          if (modal) {
            console.log('✅ A MODAL/DIALOG OPENED!');
            console.log('');
            console.log('  Checking for share URL in modal...');

            const modalHtml = modal.outerHTML;
            const shareMatches = modalHtml.match(/\/share\/[a-zA-Z0-9]{8,20}/g);

            if (shareMatches) {
              const uniqueUrls = [...new Set(shareMatches)];
              console.log(`  🎉 Found ${uniqueUrls.length} share URL(s):`);
              uniqueUrls.forEach((url, idx) => {
                console.log(`    ${idx + 1}. ${url}`);
              });
              console.log('');
              console.log('✅ SOLUTION: Click listing → Extract share URL from modal!');
            } else {
              console.log('  ❌ No share URLs in modal');

              // Check modal content
              const modalText = modal.textContent;
              console.log(`  Modal has ${modalText.length} characters of text`);

              // Look for links in modal
              const modalLinks = modal.querySelectorAll('a[href]');
              console.log(`  Modal has ${modalLinks.length} links`);

              if (modalLinks.length > 0) {
                console.log('  Links in modal:');
                modalLinks.forEach((link, idx) => {
                  console.log(`    ${idx + 1}. ${link.href}`);
                });
              }
            }

            console.log('');
            console.log('  To close modal: Press ESC or click outside');

          } else {
            console.log('⚠️  URL did not change and no modal opened');
            console.log('  This is unexpected. Try clicking manually to see what happens.');
          }
        }

        console.log('='.repeat(80));

      }, 2000);

    }, 500);

  }, 3000);
}
