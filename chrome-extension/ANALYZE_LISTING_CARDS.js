/**
 * DEBUG SCRIPT - Analyze listing card structure
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 *
 * This script examines the HTML structure of listing cards to find:
 * - Listing IDs or share IDs
 * - Links to listing pages
 * - Data attributes that might contain URLs
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Analyze Listing Card Structure');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

// Find Share buttons
console.log('🔍 Step 1: Finding Share buttons...');
let shareButtons = Array.from(document.querySelectorAll('[aria-label="Share"]'));

if (shareButtons.length === 0) {
  // Fallback: search by text
  const allClickable = document.querySelectorAll('div[role="button"], span, a, button');
  shareButtons = Array.from(allClickable).filter(el => {
    const text = el.textContent.trim();
    return text === 'Share' || text === 'share';
  });
}

console.log(`✅ Found ${shareButtons.length} Share buttons\n`);

if (shareButtons.length === 0) {
  console.log('❌ No Share buttons found. Make sure you scrolled down to see listings.');
} else {
  // Analyze first 3 listing cards
  const maxToAnalyze = Math.min(shareButtons.length, 3);
  console.log(`🔬 Analyzing first ${maxToAnalyze} listing cards...\n`);

  for (let i = 0; i < maxToAnalyze; i++) {
    const shareButton = shareButtons[i];

    console.log('='.repeat(80));
    console.log(`📦 LISTING ${i + 1}/${maxToAnalyze}`);
    console.log('='.repeat(80));

    // Find the listing card container (parent elements)
    console.log('\n🔍 Finding listing card container...');

    // Try to find the card by going up the DOM tree
    let container = shareButton;
    let depth = 0;
    const maxDepth = 15;

    while (container && depth < maxDepth) {
      // Check if this element looks like a listing card
      const hasImage = container.querySelector('img') !== null;
      const hasPrice = container.textContent.match(/\$\d+/);
      const hasButtons = container.querySelectorAll('[role="button"]').length > 2;

      if (hasImage && hasPrice && hasButtons) {
        console.log(`  ✅ Found potential card container at depth ${depth}`);
        console.log(`     Tag: ${container.tagName}`);
        console.log(`     Classes: ${container.className || '(none)'}`);
        break;
      }

      container = container.parentElement;
      depth++;
    }

    if (!container || depth >= maxDepth) {
      console.log('  ⚠️  Could not find card container');
      container = shareButton.parentElement.parentElement.parentElement;
      console.log(`  Using Share button parent^3 as container`);
    }

    // Analyze the container
    console.log('\n📊 Container Analysis:');
    console.log(`  Tag: <${container.tagName}>`);
    console.log(`  ID: ${container.id || '(none)'}`);
    console.log(`  Classes: ${container.className || '(none)'}`);

    // Check for data attributes
    console.log('\n  Data Attributes:');
    const attrs = container.attributes;
    let foundDataAttrs = false;
    for (let j = 0; j < attrs.length; j++) {
      const attr = attrs[j];
      if (attr.name.startsWith('data-')) {
        console.log(`    ${attr.name}: ${attr.value.substring(0, 100)}`);
        foundDataAttrs = true;
      }
    }
    if (!foundDataAttrs) {
      console.log('    (none found)');
    }

    // Look for links in the container
    console.log('\n  🔗 Links in container:');
    const links = container.querySelectorAll('a[href]');
    console.log(`    Total links: ${links.length}`);

    if (links.length > 0) {
      links.forEach((link, idx) => {
        const href = link.getAttribute('href');
        const text = link.textContent.trim().substring(0, 40);
        console.log(`    ${idx + 1}. ${href.substring(0, 80)}`);
        console.log(`       Text: "${text}"`);

        // Check if it's a listing URL
        if (href.includes('/item/') || href.includes('/share/') || href.includes('listing_id=')) {
          console.log(`       ⭐ POTENTIAL LISTING URL!`);
        }
      });
    } else {
      console.log('    (no links found)');
    }

    // Look for images
    console.log('\n  🖼️  Images in container:');
    const images = container.querySelectorAll('img');
    console.log(`    Total images: ${images.length}`);

    if (images.length > 0) {
      images.forEach((img, idx) => {
        const src = img.src || img.getAttribute('data-src');
        const alt = img.alt || '(no alt)';
        console.log(`    ${idx + 1}. Alt: "${alt.substring(0, 40)}"`);
        console.log(`       Src: ${src ? src.substring(0, 80) : '(none)'}`);
      });
    }

    // Check Share button attributes
    console.log('\n  ⚡ Share Button Analysis:');
    console.log(`    Tag: <${shareButton.tagName}>`);
    console.log(`    Aria-label: ${shareButton.getAttribute('aria-label') || '(none)'}`);

    // Check Share button data attributes
    console.log('    Data Attributes:');
    const shareAttrs = shareButton.attributes;
    let foundShareData = false;
    for (let j = 0; j < shareAttrs.length; j++) {
      const attr = shareAttrs[j];
      if (attr.name.startsWith('data-')) {
        console.log(`      ${attr.name}: ${attr.value.substring(0, 100)}`);
        foundShareData = true;
      }
    }
    if (!foundShareData) {
      console.log('      (none found)');
    }

    // Look for the "..." menu button and analyze it
    console.log('\n  📋 Looking for "..." menu button...');
    const menuButtons = container.querySelectorAll('[aria-label*="More"], [aria-label*="Actions"]');

    if (menuButtons.length > 0) {
      console.log(`    Found ${menuButtons.length} potential menu button(s)`);
      menuButtons.forEach((btn, idx) => {
        console.log(`    Menu ${idx + 1}:`);
        console.log(`      Aria-label: ${btn.getAttribute('aria-label')}`);
        console.log(`      Tag: <${btn.tagName}>`);

        // Check for data attributes
        const menuAttrs = btn.attributes;
        for (let j = 0; j < menuAttrs.length; j++) {
          const attr = menuAttrs[j];
          if (attr.name.startsWith('data-')) {
            console.log(`      ${attr.name}: ${attr.value.substring(0, 100)}`);
          }
        }
      });
    } else {
      console.log('    No menu buttons found with "More" or "Actions" aria-label');
    }

    // Search the entire container HTML for IDs or URLs
    console.log('\n  🔎 Searching HTML source for patterns...');
    const html = container.outerHTML;

    // Look for share URLs
    const shareMatches = html.match(/\/share\/[a-zA-Z0-9]{8,20}/g);
    if (shareMatches && shareMatches.length > 0) {
      console.log(`    ⭐⭐⭐ FOUND SHARE URLs: ${shareMatches.length}`);
      shareMatches.forEach((match, idx) => {
        console.log(`      ${idx + 1}. ${match}`);
      });
    } else {
      console.log('    Share URLs (/share/XXX): not found');
    }

    // Look for item URLs
    const itemMatches = html.match(/\/item\/\d{15,20}/g);
    if (itemMatches && itemMatches.length > 0) {
      console.log(`    ⭐⭐⭐ FOUND ITEM URLs: ${itemMatches.length}`);
      itemMatches.forEach((match, idx) => {
        console.log(`      ${idx + 1}. ${match}`);
      });
    } else {
      console.log('    Item URLs (/item/XXX): not found');
    }

    // Look for listing_id parameter
    const listingIdMatches = html.match(/listing_id=(\d{15,20})/g);
    if (listingIdMatches && listingIdMatches.length > 0) {
      console.log(`    ⭐⭐⭐ FOUND LISTING IDs: ${listingIdMatches.length}`);
      listingIdMatches.forEach((match, idx) => {
        console.log(`      ${idx + 1}. ${match}`);
      });
    } else {
      console.log('    Listing IDs (listing_id=XXX): not found');
    }

    // Look for any long numeric IDs (15-20 digits)
    const numericIds = html.match(/["':=]\d{15,20}["',\s]/g);
    if (numericIds && numericIds.length > 0) {
      console.log(`    Long numeric IDs (15-20 digits): ${numericIds.length} found`);
      // Show unique IDs only
      const uniqueIds = [...new Set(numericIds)];
      uniqueIds.slice(0, 5).forEach((id, idx) => {
        console.log(`      ${idx + 1}. ${id.trim()}`);
      });
      if (uniqueIds.length > 5) {
        console.log(`      ... and ${uniqueIds.length - 5} more`);
      }
    } else {
      console.log('    Long numeric IDs: not found');
    }

    console.log('\n');
  }

  // Final summary
  console.log('='.repeat(80));
  console.log('💡 SUMMARY & RECOMMENDATIONS');
  console.log('='.repeat(80));
  console.log('Based on the analysis above, we need to:');
  console.log('1. Check if any links in the listing card lead to listing pages');
  console.log('2. Check if clicking the listing image opens a listing page');
  console.log('3. Check if there are any data attributes with listing IDs');
  console.log('4. Try clicking the Share button to see if the URL is in the dialog');
  console.log('5. Try clicking the "..." menu to see if share link is there');
  console.log('');
  console.log('Run EXTRACT_VIA_SHARE_BUTTONS.js to test Share button approach');
  console.log('='.repeat(80));
}
