/**
 * DEBUG SCRIPT - Extract share links from dropdown menus
 * Run this in the browser console on your Facebook Marketplace selling page
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Extract Links from Menus');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

// Find all "..." menu buttons
console.log('🔍 Looking for menu buttons...');

// Facebook uses various attributes for these buttons
const possibleMenuSelectors = [
  'div[aria-label="More"]',
  'div[aria-label="More options"]',
  'div[role="button"][aria-label*="More"]',
  'div[role="button"][aria-haspopup="menu"]',
  'i[data-visualcompletion="css-img"]',
  '[aria-label="Actions for this item"]',
  '[aria-label*="..."]'
];

let menuButtons = [];
for (const selector of possibleMenuSelectors) {
  try {
    const buttons = document.querySelectorAll(selector);
    if (buttons.length > 0) {
      console.log(`  Found ${buttons.length} buttons with selector: ${selector}`);
      menuButtons = Array.from(buttons);
      break;
    }
  } catch (e) {
    // Invalid selector
  }
}

if (menuButtons.length === 0) {
  console.log('❌ No menu buttons found! Trying alternative approach...');
  // Try finding clickable elements near listing cards
  const allDivs = document.querySelectorAll('div[role="button"]');
  console.log(`  Found ${allDivs.length} div[role="button"] elements total`);

  // Show samples
  console.log('\n  Showing first 10 button aria-labels:');
  Array.from(allDivs).slice(0, 10).forEach((div, idx) => {
    const label = div.getAttribute('aria-label') || '(no label)';
    console.log(`    ${idx + 1}. ${label}`);
  });
} else {
  console.log(`\n✅ Found ${menuButtons.length} menu buttons!`);
}

console.log('\n' + '='.repeat(80));
console.log('🎯 AUTOMATED EXTRACTION (will click menus)');
console.log('='.repeat(80));

// Function to wait
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Function to close any open menus
function closeMenus() {
  // Press Escape key
  document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', keyCode: 27 }));
  // Also try clicking outside
  document.body.click();
}

// Main extraction function
async function extractShareLinks() {
  const shareLinks = [];
  const maxButtons = Math.min(menuButtons.length, 5); // Test on first 5 only

  console.log(`\nTesting first ${maxButtons} listings...\n`);

  for (let i = 0; i < maxButtons; i++) {
    const button = menuButtons[i];

    console.log(`\n📦 Listing ${i + 1}/${maxButtons}`);
    console.log('  Clicking menu button...');

    try {
      // Close any open menus first
      closeMenus();
      await wait(300);

      // Scroll button into view
      button.scrollIntoView({ behavior: 'smooth', block: 'center' });
      await wait(500);

      // Click the menu button
      button.click();
      await wait(1000); // Wait for menu to appear

      console.log('  Menu opened, looking for Share/Copy link...');

      // Look for "Share" or "Copy link" in the dropdown
      const allMenuItems = document.querySelectorAll('[role="menuitem"], a, div[role="button"]');

      let foundLink = null;
      for (const item of allMenuItems) {
        const text = item.textContent || '';
        const href = item.getAttribute('href') || '';

        // Look for "Copy link" or share links
        if (text.includes('Copy link') || text.includes('Share')) {
          console.log(`    Found: "${text.substring(0, 50)}"`);

          // Check if it has a href with /share/
          if (href.includes('/share/')) {
            foundLink = href;
            console.log(`    ✅ Share URL: ${href}`);
            break;
          }

          // Or check nested links
          const nestedLink = item.querySelector('a[href*="/share/"]');
          if (nestedLink) {
            foundLink = nestedLink.getAttribute('href');
            console.log(`    ✅ Share URL (nested): ${foundLink}`);
            break;
          }
        }
      }

      if (foundLink) {
        // Clean up the URL
        if (foundLink.startsWith('/')) {
          foundLink = 'https://www.facebook.com' + foundLink;
        }
        foundLink = foundLink.split('?')[0]; // Remove query params

        shareLinks.push(foundLink);
        console.log(`  ✅ Extracted: ${foundLink}`);
      } else {
        console.log('  ⚠️  No share link found in menu');
      }

      // Close the menu
      closeMenus();
      await wait(500);

    } catch (err) {
      console.error(`  ❌ Error: ${err.message}`);
      closeMenus();
    }
  }

  // Final results
  console.log('\n' + '='.repeat(80));
  console.log('📊 EXTRACTION RESULTS');
  console.log('='.repeat(80));
  console.log(`Total share links found: ${shareLinks.length}`);

  if (shareLinks.length > 0) {
    console.log('\nExtracted URLs:');
    shareLinks.forEach((url, idx) => {
      console.log(`  ${idx + 1}. ${url}`);
    });

    console.log('\n✅ SUCCESS! Extension can use this approach to extract all listings.');
    console.log('\nNext step: Update content.js to:');
    console.log('  1. Find all menu buttons');
    console.log('  2. Click each one');
    console.log('  3. Extract share link from dropdown');
    console.log('  4. Navigate to share URL and extract data');
  } else {
    console.log('\n❌ No share links found');
    console.log('Need to investigate dropdown menu structure');
  }

  console.log('='.repeat(80));

  return shareLinks;
}

// Run the extraction
if (menuButtons.length > 0) {
  console.log('\n⚡ Starting automated extraction in 2 seconds...');
  console.log('(Watch your screen - menus will open/close automatically)\n');

  setTimeout(() => {
    extractShareLinks().then(links => {
      console.log('\n✅ Extraction complete!');
      window.extractedShareLinks = links;
      console.log('Links saved to: window.extractedShareLinks');
    });
  }, 2000);
} else {
  console.log('\n❌ Cannot proceed - no menu buttons found');
  console.log('Please scroll down to load listings, then run this script again');
}
