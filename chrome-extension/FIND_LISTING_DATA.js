/**
 * FIND LISTING DATA - Search for listing IDs in the page
 * Facebook may have the data in the HTML but not as clickable links
 */

(function findListingData() {
  console.log('🔍 SEARCHING FOR LISTING DATA IN PAGE...');
  console.log('='.repeat(80));

  // Method 1: Search page HTML for listing/item IDs
  const html = document.documentElement.outerHTML;

  console.log('\n1️⃣ SEARCHING HTML FOR PATTERNS:');
  console.log('-'.repeat(80));

  // Search for item IDs
  const itemIdMatches = html.match(/item[_\-\/](\d{15,20})/gi);
  if (itemIdMatches) {
    const uniqueIds = [...new Set(itemIdMatches)];
    console.log('Found ' + uniqueIds.length + ' unique item ID patterns:');
    uniqueIds.slice(0, 10).forEach((match, i) => {
      console.log('  ' + (i + 1) + '. ' + match);
    });
  } else {
    console.log('No item ID patterns found');
  }

  // Search for listing IDs
  const listingIdMatches = html.match(/listing[_\-\/](\d{15,20})/gi);
  if (listingIdMatches) {
    const uniqueIds = [...new Set(listingIdMatches)];
    console.log('\nFound ' + uniqueIds.length + ' unique listing ID patterns:');
    uniqueIds.slice(0, 10).forEach((match, i) => {
      console.log('  ' + (i + 1) + '. ' + match);
    });
  } else {
    console.log('No listing ID patterns found');
  }

  // Search for product IDs
  const productIdMatches = html.match(/product[_\-\/]id[=:]?(\d{15,20})/gi);
  if (productIdMatches) {
    const uniqueIds = [...new Set(productIdMatches)];
    console.log('\nFound ' + uniqueIds.length + ' unique product ID patterns:');
    uniqueIds.slice(0, 10).forEach((match, i) => {
      console.log('  ' + (i + 1) + '. ' + match);
    });
  }

  console.log('\n\n2️⃣ CHECKING FOR DATA ATTRIBUTES:');
  console.log('-'.repeat(80));

  // Check for elements with data attributes
  const dataElements = document.querySelectorAll('[data-testid], [data-id], [data-product-id], [data-listing-id], [data-item-id]');
  console.log('Elements with data attributes: ' + dataElements.length);

  if (dataElements.length > 0) {
    console.log('\nSample data attributes:');
    const seen = new Set();
    dataElements.forEach((el, i) => {
      if (i < 20) {
        const testId = el.getAttribute('data-testid');
        const id = el.getAttribute('data-id');
        const productId = el.getAttribute('data-product-id');
        const listingId = el.getAttribute('data-listing-id');
        const itemId = el.getAttribute('data-item-id');

        if (testId && !seen.has(testId)) {
          console.log('  data-testid: ' + testId);
          seen.add(testId);
        }
        if (id) console.log('  data-id: ' + id);
        if (productId) console.log('  data-product-id: ' + productId);
        if (listingId) console.log('  data-listing-id: ' + listingId);
        if (itemId) console.log('  data-item-id: ' + itemId);
      }
    });
  }

  console.log('\n\n3️⃣ CHECKING IMAGE CONTAINERS FOR CLICKABLE ELEMENTS:');
  console.log('-'.repeat(80));

  const images = document.querySelectorAll('img[src*="scontent"]');
  console.log('Total listing images: ' + images.length);

  // Check if there are clickable divs or spans near images
  let clickableCount = 0;
  images.forEach((img, i) => {
    if (i < 5) {
      // Walk up the parent tree looking for clickable elements
      let parent = img.parentElement;
      let depth = 0;

      while (parent && depth < 10) {
        const role = parent.getAttribute('role');
        const onClick = parent.getAttribute('onclick') || parent.onclick;
        const tabIndex = parent.getAttribute('tabindex');

        if (role === 'button' || role === 'link' || onClick || tabIndex === '0') {
          clickableCount++;
          console.log('\nImage ' + (i + 1) + ' is inside clickable element:');
          console.log('  Tag: ' + parent.tagName);
          console.log('  Role: ' + (role || 'none'));
          console.log('  Tabindex: ' + (tabIndex || 'none'));
          console.log('  Has onclick: ' + (!!onClick));

          // Check for any IDs in this element or its attributes
          const innerHTML = parent.innerHTML.substring(0, 500);
          const idMatch = innerHTML.match(/\d{15,20}/);
          if (idMatch) {
            console.log('  Found ID: ' + idMatch[0]);
          }

          break;
        }

        depth++;
        parent = parent.parentElement;
      }
    }
  });

  console.log('\nClickable containers found: ' + clickableCount + ' / ' + Math.min(images.length, 5));

  console.log('\n\n4️⃣ CHECKING REACT/JAVASCRIPT STATE:');
  console.log('-'.repeat(80));

  // Check for React state
  const reactRoot = document.querySelector('[data-reactroot], #mount_0_0');
  if (reactRoot) {
    console.log('Found React root element');

    // Try to find React fiber
    const fiberKey = Object.keys(reactRoot).find(key => key.startsWith('__reactFiber'));
    if (fiberKey) {
      console.log('React fiber key found: ' + fiberKey);
    }
  }

  // Check for JSON-LD or other structured data
  const scripts = document.querySelectorAll('script[type="application/json"], script[type="application/ld+json"]');
  console.log('JSON script tags: ' + scripts.length);
  if (scripts.length > 0) {
    scripts.forEach((script, i) => {
      if (i < 3) {
        try {
          const data = JSON.parse(script.textContent);
          console.log('\nScript ' + (i + 1) + ' contains:', Object.keys(data).slice(0, 10));
        } catch (e) {
          // Not valid JSON
        }
      }
    });
  }

  console.log('\n\n='.repeat(80));
  console.log('✅ ANALYSIS COMPLETE');
  console.log('='.repeat(80));

  console.log('\nKEY FINDINGS:');
  console.log('- Listing images: ' + images.length);
  console.log('- Item links: 0 (Facebook removed them)');
  console.log('- Data attributes: ' + dataElements.length);
  console.log('- Clickable containers: ' + clickableCount);

  console.log('\nNEXT STEPS:');
  console.log('We need to find an alternative extraction method since Facebook');
  console.log('no longer provides direct links to items on the selling page.');
  console.log('\nPossible solutions:');
  console.log('1. Use Facebook Graph API (requires auth token)');
  console.log('2. Simulate clicks on listing cards and extract from detail view');
  console.log('3. Extract data from page\'s JavaScript state');
  console.log('4. Use the /marketplace/edit/?listing_id= URLs found earlier');

})();
