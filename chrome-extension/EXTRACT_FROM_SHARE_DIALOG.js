/**
 * DEBUG SCRIPT - Extract share URL from Share button dialog
 * Specifically looks at the BOTTOM "Copy link" element
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Extract from Share Dialog Bottom Link');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function closeDialogs() {
  document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', keyCode: 27 }));
  const backdrop = document.querySelector('[role="dialog"]');
  if (backdrop && backdrop.parentElement) {
    backdrop.parentElement.click();
  }
}

// Find Share buttons
console.log('🔍 Finding Share buttons...');
let shareButtons = Array.from(document.querySelectorAll('[aria-label*="Share"]'))
  .filter(btn => {
    const label = btn.getAttribute('aria-label');
    return label && label.startsWith('Share ') && !label.includes('More options');
  });

console.log(`✅ Found ${shareButtons.length} Share buttons\n`);

if (shareButtons.length === 0) {
  console.log('❌ No Share buttons found. Scroll down and try again.');
} else {
  async function testShareDialog() {
    // Test first 3 listings
    const maxToTest = Math.min(shareButtons.length, 3);
    console.log(`🧪 Testing first ${maxToTest} Share dialogs...\n`);

    const results = [];

    for (let i = 0; i < maxToTest; i++) {
      const shareBtn = shareButtons[i];
      const ariaLabel = shareBtn.getAttribute('aria-label');
      const titleMatch = ariaLabel.match(/Share (.+)/);
      const title = titleMatch ? titleMatch[1] : 'Unknown';

      console.log('='.repeat(80));
      console.log(`📦 LISTING ${i + 1}/${maxToTest}`);
      console.log('='.repeat(80));
      console.log(`Title: "${title.substring(0, 60)}"`);
      console.log('');

      try {
        // Close any open dialogs
        closeDialogs();
        await wait(300);

        // Scroll into view
        shareBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await wait(500);

        // Click Share button
        console.log('🖱️  Clicking Share button...');
        shareBtn.click();
        await wait(1500);

        // Find the dialog
        const dialog = document.querySelector('[role="dialog"]');

        if (!dialog) {
          console.log('❌ No dialog appeared\n');
          continue;
        }

        console.log('✅ Share dialog opened');
        console.log('');

        // Get ALL elements in dialog
        console.log('🔍 Analyzing dialog structure...');
        const allElements = dialog.querySelectorAll('*');
        console.log(`  Total elements in dialog: ${allElements.length}`);
        console.log('');

        // Find all links in dialog
        const allLinks = dialog.querySelectorAll('a[href]');
        console.log(`  Links (<a href>) in dialog: ${allLinks.length}`);

        if (allLinks.length > 0) {
          console.log('  All links:');
          allLinks.forEach((link, idx) => {
            const href = link.getAttribute('href');
            const text = link.textContent.trim();
            console.log(`    ${idx + 1}. ${href}`);
            console.log(`       Text: "${text.substring(0, 50)}"`);

            // Check if this is a share URL
            if (href.includes('/share/')) {
              console.log(`       🎉 THIS IS A SHARE URL!`);
            }
          });
          console.log('');
        }

        // Look for "Copy link" text specifically
        console.log('🔍 Looking for "Copy link" element...');
        const copyLinkElements = Array.from(allElements).filter(el => {
          const text = el.textContent.trim();
          return text === 'Copy link' || text.includes('Copy link');
        });

        console.log(`  Found ${copyLinkElements.length} "Copy link" elements`);

        if (copyLinkElements.length > 0) {
          copyLinkElements.forEach((el, idx) => {
            console.log(`  Copy link ${idx + 1}:`);
            console.log(`    Tag: <${el.tagName}>`);
            console.log(`    Text: "${el.textContent.trim()}"`);
            console.log(`    Href: ${el.getAttribute('href') || '(none)'}`);

            // Check parent and siblings
            const parent = el.parentElement;
            console.log(`    Parent tag: <${parent ? parent.tagName : 'none'}>`);

            if (parent) {
              // Check for href in parent
              const parentHref = parent.getAttribute('href');
              if (parentHref) {
                console.log(`    Parent href: ${parentHref}`);
                if (parentHref.includes('/share/')) {
                  console.log(`    🎉🎉🎉 FOUND SHARE URL IN PARENT!`);
                }
              }

              // Check siblings
              const siblings = Array.from(parent.children);
              siblings.forEach((sibling, idx) => {
                const siblingHref = sibling.getAttribute('href');
                if (siblingHref && siblingHref.includes('/share/')) {
                  console.log(`    Sibling ${idx}: ${siblingHref}`);
                  console.log(`    🎉🎉🎉 FOUND SHARE URL IN SIBLING!`);
                }
              });

              // Look in parent's parent
              const grandparent = parent.parentElement;
              if (grandparent) {
                const grandLinks = grandparent.querySelectorAll('a[href*="/share/"]');
                if (grandLinks.length > 0) {
                  console.log(`    Found ${grandLinks.length} share link(s) in grandparent container:`);
                  grandLinks.forEach((link, idx) => {
                    console.log(`      ${idx + 1}. ${link.getAttribute('href')}`);
                  });
                }
              }
            }
            console.log('');
          });
        }

        // Search entire dialog HTML for share URLs
        console.log('🔎 Searching entire dialog HTML for /share/ pattern...');
        const dialogHtml = dialog.outerHTML;
        const shareMatches = dialogHtml.match(/\/share\/[a-zA-Z0-9]{8,20}/g);

        if (shareMatches && shareMatches.length > 0) {
          const uniqueUrls = [...new Set(shareMatches)];
          console.log(`  ✅ Found ${uniqueUrls.length} share URL(s) in dialog HTML:`);
          uniqueUrls.forEach((url, idx) => {
            console.log(`    ${idx + 1}. ${url}`);

            const fullUrl = `https://www.facebook.com${url}`;
            results.push({
              title: title,
              shareUrl: fullUrl,
              shareId: url.replace('/share/', '')
            });
          });
          console.log('');
          console.log(`  🎉 SUCCESS! Found share URL for this listing!`);
        } else {
          console.log(`  ❌ No /share/ URLs found in dialog HTML`);
        }

        // Look at the very LAST element in the dialog
        console.log('');
        console.log('🔍 Checking the VERY LAST elements in dialog...');
        const lastElements = Array.from(allElements).slice(-10);
        console.log(`  Last 10 elements:`);
        lastElements.forEach((el, idx) => {
          const tag = el.tagName;
          const text = el.textContent.trim().substring(0, 30);
          const href = el.getAttribute('href');
          console.log(`    ${idx + 1}. <${tag}> text="${text}" href="${href || '(none)'}"`);
        });

        console.log('\n');

        // Close dialog
        closeDialogs();
        await wait(500);

      } catch (err) {
        console.error(`❌ Error: ${err.message}\n`);
        closeDialogs();
      }
    }

    // Final summary
    console.log('='.repeat(80));
    console.log('📊 FINAL RESULTS');
    console.log('='.repeat(80));
    console.log('');

    if (results.length > 0) {
      console.log(`✅ Successfully extracted ${results.length} share URLs!`);
      console.log('');
      results.forEach((item, idx) => {
        console.log(`${idx + 1}. "${item.title.substring(0, 50)}"`);
        console.log(`   ${item.shareUrl}`);
        console.log('');
      });

      window.extractedShareUrls = results;
      console.log('Saved to: window.extractedShareUrls');
    } else {
      console.log('❌ No share URLs extracted');
      console.log('');
      console.log('💡 The share URL might be:');
      console.log('  - Hidden in a data attribute');
      console.log('  - Generated only when "Copy link" is clicked');
      console.log('  - Stored in JavaScript variables');
    }

    console.log('='.repeat(80));
  }

  // Run test
  console.log('⏳ Starting test in 2 seconds...\n');
  setTimeout(() => {
    testShareDialog().then(() => {
      console.log('\n✅ Test complete!');
    });
  }, 2000);
}
