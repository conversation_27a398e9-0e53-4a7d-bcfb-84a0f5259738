<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      width: 350px;
      padding: 0;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      background: #0d1117;
      color: #e6edf3;
    }
    
    .header {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
      padding: 20px;
      border-bottom: 1px solid #30363d;
    }
    
    h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .subtitle {
      color: #8b949e;
      font-size: 12px;
      margin-top: 4px;
    }
    
    .content {
      padding: 20px;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 14px;
    }
    
    input, select {
      width: 100%;
      padding: 10px;
      border: 2px solid #30363d;
      border-radius: 8px;
      background: #161b22;
      color: #e6edf3;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    input:focus, select:focus {
      outline: none;
      border-color: #238636;
    }
    
    .btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .btn-primary {
      background: #238636;
      color: white;
    }
    
    .btn-primary:hover {
      background: #2ea043;
    }
    
    .btn-primary:disabled {
      background: #30363d;
      cursor: not-allowed;
    }

    .btn-warning {
      background: #9e6a03;
      color: white;
    }

    .btn-warning:hover {
      background: #bb7506;
    }

    .btn-danger {
      background: #da3633;
      color: white;
    }

    .btn-danger:hover {
      background: #e5534b;
    }

    .btn-group {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }

    .btn-group .btn {
      flex: 1;
    }

    .controls {
      display: none;
    }

    .controls.active {
      display: block;
    }

    .status {
      margin-top: 15px;
      padding: 12px;
      border-radius: 8px;
      font-size: 13px;
      display: none;
    }
    
    .status.success {
      background: #238636;
      display: block;
    }
    
    .status.error {
      background: #da3633;
      display: block;
    }
    
    .status.info {
      background: #0969da;
      display: block;
    }
    
    .progress {
      margin-top: 15px;
      display: none;
    }
    
    .progress-bar {
      width: 100%;
      height: 8px;
      background: #30363d;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .progress-fill {
      height: 100%;
      background: #238636;
      transition: width 0.3s;
      width: 0%;
    }
    
    .progress-text {
      font-size: 12px;
      color: #8b949e;
      margin-top: 8px;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🛒 OneOf Store Importer</h1>
    <div class="subtitle">Import Facebook Marketplace listings</div>
  </div>
  
  <div class="content">
    <div class="form-group">
      <label for="apiUrl">OneOf Store API:</label>
      <input type="text" id="apiUrl" value="https://oneofstore.com/api" placeholder="https://oneofstore.com/api">
    </div>

    <div class="form-group">
      <label style="display: flex; align-items: center; cursor: pointer;">
        <input type="checkbox" id="enhanceLLM" checked style="width: auto; margin-right: 10px;">
        <span>🤖 Enhance descriptions with AI</span>
      </label>
      <div style="font-size: 11px; color: #8b949e; margin-top: 4px; margin-left: 24px;">
        Uses OpenRouter AI to improve titles & descriptions
      </div>
    </div>

    <div class="form-group">
      <label style="display: flex; align-items: center; cursor: pointer;">
        <input type="checkbox" id="skipScroll" style="width: auto; margin-right: 10px;">
        <span>⚡ Skip auto-scroll (use if already scrolled)</span>
      </label>
      <div style="font-size: 11px; color: #8b949e; margin-top: 4px; margin-left: 24px;">
        Check this if you already manually scrolled to load all listings
      </div>
    </div>

    <div style="background: #161b22; border: 1px solid #30363d; padding: 12px; border-radius: 8px; margin-bottom: 20px;">
      <div style="font-size: 12px; color: #8b949e; line-height: 1.5;">
        💡 Will automatically detect and import <strong style="color: #e6edf3;">ALL new listings</strong> from your Facebook Marketplace
      </div>
    </div>

    <button id="importBtn" class="btn btn-primary">
      Import ALL New Listings
    </button>

    <div id="controls" class="controls">
      <div class="btn-group">
        <button id="pauseBtn" class="btn btn-warning">⏸️ Pause</button>
        <button id="stopBtn" class="btn btn-danger">⏹️ Stop</button>
      </div>
    </div>

    <div id="progress" class="progress">
      <div class="progress-bar">
        <div id="progressFill" class="progress-fill"></div>
      </div>
      <div id="progressText" class="progress-text"></div>
    </div>

    <div id="status" class="status"></div>
  </div>
  
  <script src="scripts/popup.js"></script>
</body>
</html>
