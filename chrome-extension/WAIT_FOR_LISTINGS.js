/**
 * WAIT FOR LISTINGS - Wait for Facebook to load the listings grid
 * Paste this in console on the selling page
 */

(async function waitAndCheck() {
  console.log('⏳ WAITING FOR LISTINGS TO LOAD...');
  console.log('='.repeat(80));

  let attempts = 0;
  const maxAttempts = 20; // 20 seconds max

  while (attempts < maxAttempts) {
    attempts++;

    // Check for various selectors that might contain listings
    const images = document.querySelectorAll('img[src*="scontent"]');
    const links = document.querySelectorAll('a');
    const itemLinks = Array.from(links).filter(a => {
      const href = a.getAttribute('href') || '';
      return href.includes('/item/');
    });

    console.log('Attempt ' + attempts + ':');
    console.log('  Total links: ' + links.length);
    console.log('  Item links: ' + itemLinks.length);
    console.log('  Listing images: ' + images.length);

    if (itemLinks.length > 0) {
      console.log('\n✅ LISTINGS LOADED!');
      console.log('Found ' + itemLinks.length + ' item links');
      console.log('');
      console.log('Sample item URLs:');
      itemLinks.slice(0, 5).forEach((link, i) => {
        console.log('  ' + (i + 1) + '. ' + link.getAttribute('href'));
      });

      console.log('');
      console.log('='.repeat(80));
      console.log('💡 The extension should work now!');
      console.log('Click the extension icon to start importing.');
      return;
    }

    // If we have images but no links, the structure might be different
    if (images.length > 10 && itemLinks.length === 0 && attempts > 5) {
      console.warn('\n⚠️  Found ' + images.length + ' images but NO item links!');
      console.warn('Facebook may have changed their structure.');
      console.warn('');
      console.warn('Let me check the parent structure of images...');

      // Check image parent structure
      images.forEach((img, i) => {
        if (i < 3) {
          console.log('\nImage ' + (i + 1) + ' parent chain:');
          let parent = img.parentElement;
          let depth = 0;
          while (parent && depth < 5) {
            const tag = parent.tagName;
            const className = parent.className ? parent.className.substring(0, 50) : '';
            const role = parent.getAttribute('role') || '';
            const ariaLabel = parent.getAttribute('aria-label') || '';

            console.log('  ' + depth + '. <' + tag + '>');
            if (className) console.log('     class: ' + className);
            if (role) console.log('     role: ' + role);
            if (ariaLabel) console.log('     aria-label: ' + ariaLabel.substring(0, 50));

            // Check if this parent is or contains a link
            if (tag === 'A') {
              const href = parent.getAttribute('href') || '';
              console.log('     href: ' + href);
            }

            depth++;
            parent = parent.parentElement;
          }
        }
      });

      break;
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  if (attempts >= maxAttempts) {
    console.error('\n❌ TIMEOUT - Listings did not load after ' + maxAttempts + ' seconds');
    console.error('');
    console.error('Possible issues:');
    console.error('1. No active listings in your account');
    console.error('2. Facebook changed their page structure');
    console.error('3. Page is still loading (try scrolling down)');
    console.error('');
    console.error('Try:');
    console.error('- Manually scroll down the page');
    console.error('- Refresh the page (F5)');
    console.error('- Check if you have any active listings');
  }

  console.log('='.repeat(80));
})();
