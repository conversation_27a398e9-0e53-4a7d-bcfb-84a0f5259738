/**
 * DEBUG SCRIPT - Extract share URLs by monitoring WhatsApp link changes
 * Wait for WhatsApp link to update before extracting
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Monitor WhatsApp Link Changes');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function closeDialogs() {
  // Press Escape
  document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', keyCode: 27 }));
  // Click body
  document.body.click();
}

// Function to extract share ID from WhatsApp link
function extractShareIdFromWhatsApp() {
  const whatsappLink = document.querySelector('a[href*="wa.me"]');

  if (!whatsappLink) {
    return null;
  }

  const href = whatsappLink.getAttribute('href');

  try {
    // Extract 'u' parameter
    const urlMatch = href.match(/[?&]u=([^&]+)/);
    if (!urlMatch) return null;

    // Decode once
    const decoded1 = decodeURIComponent(urlMatch[1]);

    // Extract 'text' parameter
    const textMatch = decoded1.match(/[?&]text=([^&]+)/);
    if (!textMatch) return null;

    // Decode again
    const shareUrl = decodeURIComponent(textMatch[1]);

    // Extract share ID
    const shareIdMatch = shareUrl.match(/\/share\/([a-zA-Z0-9]+)/);
    return shareIdMatch ? shareIdMatch[1] : null;
  } catch (err) {
    return null;
  }
}

// Wait for WhatsApp link to change to a different share ID
async function waitForNewShareId(previousShareId, maxAttempts = 20) {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    await wait(300);

    const currentShareId = extractShareIdFromWhatsApp();

    if (currentShareId && currentShareId !== previousShareId) {
      return currentShareId;
    }
  }

  return null; // Timeout - link didn't change
}

// Find Share buttons
console.log('🔍 Finding Share buttons...');
let shareButtons = Array.from(document.querySelectorAll('[aria-label*="Share"]'))
  .filter(btn => {
    const label = btn.getAttribute('aria-label');
    return label && label.startsWith('Share ') && !label.includes('More options');
  });

console.log(`✅ Found ${shareButtons.length} Share buttons\n`);

if (shareButtons.length === 0) {
  console.log('❌ No Share buttons found. Scroll down and try again.');
} else {
  async function extractShareUrls() {
    const maxToTest = Math.min(shareButtons.length, 5);
    console.log(`🧪 Extracting share URLs from first ${maxToTest} listings...\n`);

    const results = [];
    let previousShareId = null;

    for (let i = 0; i < maxToTest; i++) {
      const shareBtn = shareButtons[i];
      const ariaLabel = shareBtn.getAttribute('aria-label');
      const titleMatch = ariaLabel.match(/Share (.+)/);
      const title = titleMatch ? titleMatch[1] : 'Unknown';

      console.log(`📦 ${i + 1}/${maxToTest}: "${title.substring(0, 50)}"`);

      try {
        // Close any dialogs first
        closeDialogs();
        await wait(500);

        // Scroll into view
        shareBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await wait(300);

        // Click Share button
        shareBtn.click();
        await wait(1500);

        // Wait for WhatsApp link to appear and be DIFFERENT from previous
        console.log(`  ⏳ Waiting for share ID to load...`);
        const shareId = await waitForNewShareId(previousShareId);

        if (!shareId) {
          console.log(`  ❌ Share ID did not load or didn't change from previous\n`);

          // Try clicking Copy link button instead
          console.log(`  🔍 Looking for "Copy link" button...`);
          const copyButtons = Array.from(document.querySelectorAll('*')).filter(el => {
            return el.textContent.trim() === 'Copy link' && el.tagName !== 'BODY';
          });

          if (copyButtons.length > 0) {
            console.log(`  ✅ Found ${copyButtons.length} "Copy link" elements, clicking last one...`);
            const copyBtn = copyButtons[copyButtons.length - 1];
            copyBtn.click();
            await wait(500);

            // Try to read clipboard
            try {
              const clipboardText = await navigator.clipboard.readText();
              console.log(`  📋 Clipboard: ${clipboardText}`);

              if (clipboardText.includes('/share/')) {
                const clipShareMatch = clipboardText.match(/\/share\/([a-zA-Z0-9]+)/);
                if (clipShareMatch) {
                  const clipShareId = clipShareMatch[1];
                  console.log(`  🎉 Got share ID from clipboard: ${clipShareId}\n`);

                  results.push({
                    title: title,
                    shareUrl: `https://www.facebook.com/share/${clipShareId}/`,
                    shareId: clipShareId
                  });

                  previousShareId = clipShareId;
                  closeDialogs();
                  await wait(500);
                  continue;
                }
              }
            } catch (clipErr) {
              console.log(`  ⚠️  Cannot read clipboard: ${clipErr.message}`);
            }
          }

          closeDialogs();
          await wait(500);
          continue;
        }

        const shareUrl = `https://www.facebook.com/share/${shareId}/`;
        console.log(`  🎉 Share URL: ${shareUrl}`);
        console.log(`  🎉 Share ID: ${shareId}\n`);

        results.push({
          title: title,
          shareUrl: shareUrl,
          shareId: shareId
        });

        previousShareId = shareId;

        // Close dialog
        closeDialogs();
        await wait(500);

      } catch (err) {
        console.error(`  ❌ Error: ${err.message}\n`);
        closeDialogs();
        await wait(500);
      }
    }

    // Final summary
    console.log('='.repeat(80));
    console.log('📊 EXTRACTION COMPLETE');
    console.log('='.repeat(80));
    console.log('');

    if (results.length > 0) {
      console.log(`✅ Successfully extracted ${results.length} share URLs!`);
      console.log('');

      // Check for duplicates
      const uniqueIds = new Set(results.map(r => r.shareId));
      if (uniqueIds.size < results.length) {
        console.log(`⚠️  WARNING: Found ${results.length - uniqueIds.size} duplicate(s)!`);
        console.log('');
      }

      results.forEach((item, idx) => {
        console.log(`${idx + 1}. "${item.title.substring(0, 50)}"`);
        console.log(`   ${item.shareUrl}`);
        console.log('');
      });

      window.extractedShareUrls = results;
      console.log('Saved to: window.extractedShareUrls');
      console.log('');

      if (uniqueIds.size === results.length) {
        console.log('🎉🎉🎉 SUCCESS - All unique share URLs! 🎉🎉🎉');
      } else {
        console.log('⚠️  Some share URLs are duplicates - Facebook dialog not updating properly');
        console.log('💡 Try using "Copy link" clipboard method instead');
      }

    } else {
      console.log('❌ No share URLs extracted');
    }

    console.log('='.repeat(80));
  }

  // Run extraction
  console.log('⏳ Starting extraction in 2 seconds...\n');
  setTimeout(() => {
    extractShareUrls().then(() => {
      console.log('\n✅ Test complete!');
    });
  }, 2000);
}
