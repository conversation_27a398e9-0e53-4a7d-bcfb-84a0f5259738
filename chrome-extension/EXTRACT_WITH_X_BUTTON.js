/**
 * DEBUG SCRIPT - Extract share URLs and PROPERLY close dialog with X button
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Extract with Proper Dialog Closing');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function closeDialogWithXButton() {
  // Find the X close button in the dialog
  const closeButtons = document.querySelectorAll('[aria-label="Close"], [aria-label="close"], [aria-label*="Close"]');

  for (const btn of closeButtons) {
    // Check if it's visible and in a dialog
    const dialog = btn.closest('[role="dialog"]');
    if (dialog) {
      console.log(`  🔘 Clicking X close button...`);
      btn.click();
      return true;
    }
  }

  // Fallback: Press Escape
  console.log(`  ⚠️  X button not found, using Escape...`);
  document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', keyCode: 27 }));
  return false;
}

// Find Share buttons
console.log('🔍 Finding Share buttons...');
let shareButtons = Array.from(document.querySelectorAll('[aria-label*="Share"]'))
  .filter(btn => {
    const label = btn.getAttribute('aria-label');
    return label && label.startsWith('Share ') && !label.includes('More options');
  });

console.log(`✅ Found ${shareButtons.length} Share buttons\n`);

if (shareButtons.length === 0) {
  console.log('❌ No Share buttons found. Scroll down and try again.');
} else {
  async function extractShareUrls() {
    const maxToTest = Math.min(shareButtons.length, 5);
    console.log(`🧪 Extracting share URLs from first ${maxToTest} listings...\n`);

    const results = [];

    for (let i = 0; i < maxToTest; i++) {
      const shareBtn = shareButtons[i];
      const ariaLabel = shareBtn.getAttribute('aria-label');
      const titleMatch = ariaLabel.match(/Share (.+)/);
      const title = titleMatch ? titleMatch[1] : 'Unknown';

      console.log(`📦 ${i + 1}/${maxToTest}: "${title.substring(0, 50)}"`);

      try {
        // Make sure no dialog is open
        if (document.querySelector('[role="dialog"]')) {
          console.log(`  🔘 Dialog already open, closing it first...`);
          closeDialogWithXButton();
          await wait(800);
        }

        // Scroll into view
        shareBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await wait(400);

        // Click Share button
        console.log(`  🖱️  Clicking Share button...`);
        shareBtn.click();
        await wait(2000);

        // Find WhatsApp link
        const whatsappLink = document.querySelector('a[href*="wa.me"]');

        if (!whatsappLink) {
          console.log(`  ❌ No WhatsApp link found\n`);
          closeDialogWithXButton();
          await wait(800);
          continue;
        }

        const href = whatsappLink.getAttribute('href');
        console.log(`  ✅ Found WhatsApp link`);

        // Decode URL to extract share URL
        try {
          const urlMatch = href.match(/[?&]u=([^&]+)/);
          if (!urlMatch) {
            console.log(`  ❌ Could not parse URL\n`);
            closeDialogWithXButton();
            await wait(800);
            continue;
          }

          const decoded1 = decodeURIComponent(urlMatch[1]);
          const textMatch = decoded1.match(/[?&]text=([^&]+)/);
          if (!textMatch) {
            console.log(`  ❌ Could not find text parameter\n`);
            closeDialogWithXButton();
            await wait(800);
            continue;
          }

          const shareUrl = decodeURIComponent(textMatch[1]);
          const shareIdMatch = shareUrl.match(/\/share\/([a-zA-Z0-9]+)/);
          const shareId = shareIdMatch ? shareIdMatch[1] : null;

          if (shareId) {
            console.log(`  🎉 Share URL: ${shareUrl}`);
            console.log(`  🎉 Share ID: ${shareId}`);

            results.push({
              title: title,
              shareUrl: shareUrl,
              shareId: shareId
            });
          } else {
            console.log(`  ⚠️  No share ID found in URL: ${shareUrl}`);
          }
        } catch (err) {
          console.log(`  ❌ Error parsing URL: ${err.message}`);
        }

        // PROPERLY close the dialog by clicking X button
        console.log(`  🔘 Closing dialog with X button...`);
        closeDialogWithXButton();
        await wait(1000); // Wait for dialog to fully close

        // Verify it closed
        const stillOpen = document.querySelector('[role="dialog"]');
        if (stillOpen) {
          console.log(`  ⚠️  Dialog still open! Trying again...`);
          closeDialogWithXButton();
          await wait(1000);
        } else {
          console.log(`  ✅ Dialog closed successfully\n`);
        }

      } catch (err) {
        console.error(`  ❌ Error: ${err.message}\n`);
        closeDialogWithXButton();
        await wait(1000);
      }
    }

    // Final summary
    console.log('='.repeat(80));
    console.log('📊 EXTRACTION COMPLETE');
    console.log('='.repeat(80));
    console.log('');

    if (results.length > 0) {
      console.log(`✅ Successfully extracted ${results.length} share URLs!`);
      console.log('');

      // Check for duplicates
      const uniqueIds = new Set(results.map(r => r.shareId));
      if (uniqueIds.size < results.length) {
        console.log(`⚠️  WARNING: Found ${results.length - uniqueIds.size} duplicate(s)!`);
        console.log('');
      }

      results.forEach((item, idx) => {
        console.log(`${idx + 1}. "${item.title.substring(0, 50)}"`);
        console.log(`   ${item.shareUrl}`);
        console.log('');
      });

      window.extractedShareUrls = results;
      console.log('Saved to: window.extractedShareUrls');
      console.log('');

      if (uniqueIds.size === results.length) {
        console.log('🎉🎉🎉 SUCCESS - All unique share URLs! 🎉🎉🎉');
        console.log('');
        console.log('The X button method WORKS!');
        console.log('Extension can now extract all your listings!');
      } else {
        console.log('⚠️  Some share URLs are duplicates');
        console.log('Need to investigate further...');
      }

    } else {
      console.log('❌ No share URLs extracted');
    }

    console.log('='.repeat(80));
  }

  // Run extraction
  console.log('⏳ Starting extraction in 2 seconds...\n');
  setTimeout(() => {
    extractShareUrls().then(() => {
      console.log('\n✅ Test complete!');
    });
  }, 2000);
}
