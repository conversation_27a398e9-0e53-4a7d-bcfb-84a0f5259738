// Run this to find how to get listing URLs from the div buttons

console.clear();
console.log('🔍 SEARCHING FOR LISTING URLs IN DIV BUTTONS...\n');

// Get a sample div button with price
const divButtons = Array.from(document.querySelectorAll('div[role="button"]')).filter(div => {
  return div.textContent.includes('$') && div.textContent.length > 20 && div.textContent.length < 500;
});

console.log('Found', divButtons.length, 'potential listing div buttons\n');

if (divButtons.length > 0) {
  const sample = divButtons[0];

  console.log('📋 ANALYZING FIRST LISTING DIV:');
  console.log('Text:', sample.textContent.substring(0, 100) + '...');
  console.log('');

  // Check all attributes
  console.log('Attributes:');
  Array.from(sample.attributes).forEach(attr => {
    console.log(' ', attr.name + ':', attr.value.substring(0, 100));
  });
  console.log('');

  // Check parent elements
  console.log('Parent chain:');
  let parent = sample.parentElement;
  let depth = 0;
  while (parent && depth < 5) {
    console.log(`  ${depth}: <${parent.tagName}>`);
    if (parent.tagName === 'A') {
      console.log('    ✅ FOUND <A> TAG!');
      console.log('    href:', parent.getAttribute('href'));
    }
    Array.from(parent.attributes).forEach(attr => {
      if (attr.name.includes('id') || attr.name.includes('data') || attr.name === 'href') {
        console.log('    ' + attr.name + ':', attr.value.substring(0, 100));
      }
    });
    parent = parent.parentElement;
    depth++;
  }
  console.log('');

  // Check for <a> tags nearby
  console.log('Nearby <a> tags:');
  const nearbyLinks = sample.querySelectorAll('a');
  console.log('  Links inside div:', nearbyLinks.length);
  nearbyLinks.forEach((link, i) => {
    if (i < 3) {
      console.log(`    ${i}: href="${link.getAttribute('href')}"`);
      console.log(`       text="${link.textContent.substring(0, 50)}..."`);
    }
  });
  console.log('');

  // Check siblings
  console.log('Checking siblings for links...');
  const siblings = Array.from(sample.parentElement?.children || []);
  siblings.forEach((sib, i) => {
    if (sib.tagName === 'A') {
      console.log('  Sibling', i, 'is <a>!');
      console.log('    href:', sib.getAttribute('href'));
    }
  });
  console.log('');

  // Try to find the actual clickable link by looking at the card container
  console.log('Looking for card container...');
  let container = sample;
  for (let i = 0; i < 10; i++) {
    container = container.parentElement;
    if (!container) break;

    // Look for <a> tags at this level
    const links = Array.from(container.querySelectorAll('a'));
    const itemLinks = links.filter(a => {
      const href = a.getAttribute('href') || '';
      return href.includes('/item/') || href.includes('/marketplace/item/');
    });

    if (itemLinks.length > 0) {
      console.log('  ✅ Found', itemLinks.length, 'item links at container level', i);
      itemLinks.slice(0, 2).forEach(link => {
        console.log('    href:', link.getAttribute('href'));
        console.log('    aria-label:', link.getAttribute('aria-label'));
      });
      break;
    }
  }
}

console.log('\n' + '='.repeat(80));
console.log('💡 RECOMMENDATION:');
console.log('Check the output above to find how to get listing URLs!');
console.log('='.repeat(80));
