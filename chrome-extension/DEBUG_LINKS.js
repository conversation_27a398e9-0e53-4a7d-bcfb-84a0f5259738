// Paste this in the Facebook Marketplace console to debug link structure

console.clear();
console.log('🔍 DEBUGGING LINK STRUCTURE ON FACEBOOK MARKETPLACE\n');

// Find div buttons with prices
const divButtons = Array.from(document.querySelectorAll('div[role="button"]')).filter(div => {
  const text = div.textContent || '';
  return text.includes('$') && text.length > 20 && text.length < 500;
});

console.log('Found', divButtons.length, 'potential listing cards\n');

if (divButtons.length > 0) {
  const sample = divButtons[0];

  console.log('📋 ANALYZING FIRST LISTING CARD:\n');
  console.log('Text preview:', sample.textContent.substring(0, 80) + '...\n');

  // Check for links INSIDE
  console.log('🔎 Strategy 1: Links INSIDE the div button:');
  const innerLinks = sample.querySelectorAll('a');
  console.log('  Total <a> tags inside:', innerLinks.length);
  innerLinks.forEach((link, i) => {
    const href = link.getAttribute('href') || '';
    if (href.includes('/item/')) {
      console.log(`  ✅ FOUND: <a href="${href}">`);
    } else if (href) {
      console.log(`  ❌ Not item link: ${href.substring(0, 50)}...`);
    }
  });
  console.log('');

  // Check SIBLINGS
  console.log('🔎 Strategy 2: Links in SIBLINGS (same parent):');
  if (sample.parentElement) {
    const siblingLinks = sample.parentElement.querySelectorAll('a[href*="/item/"]');
    console.log('  Item links in parent:', siblingLinks.length);
    Array.from(siblingLinks).slice(0, 3).forEach(link => {
      console.log(`  ✅ ${link.getAttribute('href')}`);
    });
  }
  console.log('');

  // Check PARENTS
  console.log('🔎 Strategy 3: Parent <a> tags (up the tree):');
  let parent = sample;
  let foundParentLink = false;
  for (let depth = 0; depth < 10; depth++) {
    parent = parent.parentElement;
    if (!parent) break;

    if (parent.tagName === 'A') {
      const href = parent.getAttribute('href') || '';
      if (href.includes('/item/')) {
        console.log(`  ✅ FOUND at depth ${depth}: ${href}`);
        foundParentLink = true;
        break;
      } else if (href) {
        console.log(`  ❌ Parent <a> at depth ${depth} but not item: ${href.substring(0, 50)}...`);
      }
    }
  }
  if (!foundParentLink) {
    console.log('  ❌ No parent <a> tags with /item/ found');
  }
  console.log('');

  // Check if clicking the div navigates
  console.log('🖱️ Strategy 4: What happens if we CLICK the div?');
  console.log('  Try this: divButtons[0].click()');
  console.log('  (This may navigate to the listing page)\n');

  // Check for onclick handlers
  console.log('🔧 Checking event listeners:');
  const handlers = getEventListeners(sample);
  if (handlers.click) {
    console.log('  ✅ Has click event listeners:', handlers.click.length);
  } else {
    console.log('  ❌ No click event listeners found');
  }
  console.log('');

  // Final recommendation
  console.log('=' + '='.repeat(79));
  console.log('💡 RECOMMENDATION:');
  console.log('=' + '='.repeat(79));

  if (innerLinks.length > 0) {
    const itemLinks = Array.from(innerLinks).filter(l => (l.getAttribute('href') || '').includes('/item/'));
    if (itemLinks.length > 0) {
      console.log('✅ Use Strategy 1: Extract links from INSIDE the div buttons');
    } else {
      console.log('⚠️  Links exist but no /item/ links - may need to click the divs');
    }
  } else if (sample.parentElement && sample.parentElement.querySelectorAll('a[href*="/item/"]').length > 0) {
    console.log('✅ Use Strategy 2: Extract links from SIBLINGS in parent container');
  } else if (foundParentLink) {
    console.log('✅ Use Strategy 3: Extract links from PARENT <a> tags');
  } else {
    console.log('⚠️  No direct links found. You may need to:');
    console.log('   1. CLICK each div button to navigate');
    console.log('   2. Extract the URL from browser location');
    console.log('   3. Go back and repeat');
    console.log('\n   OR use a different selector to find the cards');
  }
  console.log('=' + '='.repeat(79));
}

// Make divButtons available for testing
window.testDivButtons = divButtons;
console.log('\n💾 Saved divButtons to window.testDivButtons for testing');
console.log('   Try: testDivButtons[0].click() to navigate to first listing\n');
