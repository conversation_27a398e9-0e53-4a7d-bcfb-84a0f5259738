/**
 * DEBUG SCRIPT - Extract ALL share URLs from page and match to listings
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Extract ALL Share URLs');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

// Step 1: Find all share URLs in page HTML
console.log('🔍 Step 1: Searching page HTML for share URLs...');
const html = document.documentElement.outerHTML;
const shareUrlPattern = /\/share\/([a-zA-Z0-9]{8,20})/g;
const allShareMatches = html.match(shareUrlPattern);

let shareUrls = [];
if (allShareMatches) {
  shareUrls = [...new Set(allShareMatches)].map(url => {
    const id = url.replace('/share/', '');
    return {
      id: id,
      url: `https://www.facebook.com/share/${id}`,
      title: null
    };
  });
  console.log(`✅ Found ${shareUrls.length} unique share URLs in HTML\n`);
} else {
  console.log('❌ No share URLs found in HTML\n');
}

// Step 2: Find all listing titles from Share buttons
console.log('🔍 Step 2: Finding listing titles from page...');
const shareBtns = Array.from(document.querySelectorAll('[aria-label*="Share"]'));
const titles = shareBtns.map(btn => {
  const label = btn.getAttribute('aria-label');
  const match = label.match(/Share (.+)/);
  return match ? match[1] : null;
}).filter(t => t !== null);

console.log(`✅ Found ${titles.length} listing titles\n`);

if (titles.length > 0) {
  console.log('First 5 titles:');
  titles.slice(0, 5).forEach((title, idx) => {
    console.log(`  ${idx + 1}. "${title.substring(0, 60)}"`);
  });
  console.log('');
}

// Step 3: Try to match share URLs to titles
console.log('🔍 Step 3: Attempting to match share URLs to titles...');
console.log('');

// Method 1: Search HTML for title + share URL proximity
const listings = [];

titles.forEach((title, idx) => {
  // Search for this title in HTML
  const titleInHtml = html.indexOf(title);

  if (titleInHtml !== -1) {
    // Find the nearest share URL (search 10000 chars before and after)
    const searchStart = Math.max(0, titleInHtml - 10000);
    const searchEnd = Math.min(html.length, titleInHtml + 10000);
    const contextHtml = html.substring(searchStart, searchEnd);

    const shareMatch = contextHtml.match(/\/share\/([a-zA-Z0-9]{8,20})/);

    if (shareMatch) {
      const shareId = shareMatch[1];
      const shareUrl = `https://www.facebook.com/share/${shareId}`;

      listings.push({
        index: idx + 1,
        title: title,
        shareUrl: shareUrl,
        shareId: shareId
      });
    }
  }
});

if (listings.length > 0) {
  console.log(`✅ Successfully matched ${listings.length} listings!\n`);
  console.log('='.repeat(80));
  console.log('📋 EXTRACTED LISTINGS');
  console.log('='.repeat(80));
  console.log('');

  listings.slice(0, 10).forEach(listing => {
    console.log(`${listing.index}. "${listing.title.substring(0, 60)}"`);
    console.log(`   Share URL: ${listing.shareUrl}`);
    console.log('');
  });

  if (listings.length > 10) {
    console.log(`... and ${listings.length - 10} more listings`);
    console.log('');
  }

  // Save to window for easy access
  window.extractedListings = listings;
  console.log('✅ Saved to: window.extractedListings');
  console.log('');

  console.log('='.repeat(80));
  console.log('🎉 SUCCESS!');
  console.log('='.repeat(80));
  console.log('');
  console.log(`Extracted ${listings.length} listings with share URLs!`);
  console.log('');
  console.log('💡 NEXT STEPS:');
  console.log('1. Update content.js to use this extraction method');
  console.log('2. Extract share URLs directly from page HTML');
  console.log('3. Navigate to each share URL and extract product data');
  console.log('4. Import to OneOfStore API');
  console.log('');
  console.log('This approach will be MUCH faster than clicking buttons!');
  console.log('='.repeat(80));

} else {
  console.log('⚠️  Could not match share URLs to titles');
  console.log('');
  console.log('Showing unmatched data:');
  console.log(`  Share URLs found: ${shareUrls.length}`);
  console.log(`  Titles found: ${titles.length}`);
  console.log('');

  if (shareUrls.length > 0) {
    console.log('Share URLs (first 10):');
    shareUrls.slice(0, 10).forEach((item, idx) => {
      console.log(`  ${idx + 1}. ${item.url}`);
    });
    console.log('');
  }
}

// Also try a simpler approach: just extract all share URLs in order
console.log('='.repeat(80));
console.log('🔍 ALTERNATIVE APPROACH: Extract share URLs in order');
console.log('='.repeat(80));
console.log('');

if (shareUrls.length > 0 && titles.length > 0) {
  const minLength = Math.min(shareUrls.length, titles.length);

  console.log(`Assuming first ${minLength} share URLs match first ${minLength} titles:`);
  console.log('');

  for (let i = 0; i < Math.min(10, minLength); i++) {
    console.log(`${i + 1}. "${titles[i].substring(0, 50)}"`);
    console.log(`   ${shareUrls[i].url}`);
    console.log('');
  }

  if (minLength > 10) {
    console.log(`... and ${minLength - 10} more`);
  }
}

console.log('='.repeat(80));
