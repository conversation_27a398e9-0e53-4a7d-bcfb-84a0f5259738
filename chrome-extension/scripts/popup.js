// Global state for pause/stop
let isPaused = false;
let isStopped = false;
let currentTab = null;
let isExtracting = false;

// Restore state when popup opens
async function restoreState() {
  try {
    const state = await chrome.storage.local.get(['extractionState']);

    if (state.extractionState && state.extractionState.isActive) {
      // Extraction is running - restore UI
      isExtracting = true;
      isPaused = state.extractionState.isPaused || false;

      const btn = document.getElementById('importBtn');
      const controls = document.getElementById('controls');
      const progress = document.getElementById('progress');
      const progressFill = document.getElementById('progressFill');
      const progressText = document.getElementById('progressText');
      const pauseBtn = document.getElementById('pauseBtn');

      // Restore UI state
      btn.disabled = true;
      btn.textContent = 'Extracting...';
      controls.classList.add('active');
      progress.style.display = 'block';

      // Restore progress
      progressFill.style.width = (state.extractionState.percent || 0) + '%';
      progressText.textContent = state.extractionState.text || 'Processing...';

      // Restore pause button state
      if (isPaused) {
        pauseBtn.textContent = '▶️ Resume';
        pauseBtn.classList.remove('btn-warning');
        pauseBtn.classList.add('btn-primary');
      }

      // Get current tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      currentTab = tab;

      console.log('Restored extraction state:', state.extractionState);
    }
  } catch (error) {
    console.error('Error restoring state:', error);
  }
}

// Call restore on popup load
restoreState();

document.getElementById('importBtn').addEventListener('click', async () => {
  console.log('🚀 IMPORT BUTTON CLICKED');

  const apiUrl = document.getElementById('apiUrl').value.trim();
  const enhanceLLM = document.getElementById('enhanceLLM').checked;
  const skipScroll = document.getElementById('skipScroll').checked;
  const btn = document.getElementById('importBtn');
  const status = document.getElementById('status');
  const progress = document.getElementById('progress');
  const progressFill = document.getElementById('progressFill');
  const progressText = document.getElementById('progressText');
  const controls = document.getElementById('controls');

  console.log('Settings:', { apiUrl, enhanceLLM, skipScroll });

  // Validate
  if (!apiUrl) {
    console.error('No API URL provided');
    showStatus('Please enter API URL', 'error');
    return;
  }

  console.log('✅ Validation passed, starting extraction...');

  // Reset state
  isPaused = false;
  isStopped = false;
  isExtracting = true;

  // Save initial state
  await chrome.storage.local.set({
    extractionState: {
      isActive: true,
      isPaused: false,
      percent: 0,
      text: 'Starting extraction...'
    }
  });

  // Disable button, show controls
  btn.disabled = true;
  btn.textContent = 'Extracting...';
  controls.classList.add('active');
  status.style.display = 'none';
  progress.style.display = 'block';
  progressFill.style.width = '10%';
  progressText.textContent = 'Starting extraction...';

  try {
    console.log('📍 STEP 1: Finding Facebook Marketplace page...');
    // STEP 1: Find or navigate to the correct Facebook Marketplace Selling page
    progressText.textContent = 'Finding Facebook Marketplace page...';

    let targetTab = null;
    const correctUrl = 'https://www.facebook.com/marketplace/you/selling';

    // Check all tabs for an existing Facebook Marketplace tab
    console.log('Querying all tabs...');
    const allTabs = await chrome.tabs.query({});
    console.log('Total tabs open:', allTabs.length);

    const fbTabs = allTabs.filter(t =>
      t.url && t.url.includes('facebook.com/marketplace')
    );

    console.log('Found ' + fbTabs.length + ' Facebook Marketplace tabs');
    fbTabs.forEach((tab, i) => {
      console.log('  FB Tab ' + (i + 1) + ':', tab.url);
    });

    // Look for a tab already on the selling page
    const sellingTab = fbTabs.find(t =>
      t.url.includes('marketplace/you/selling') ||
      t.url.includes('marketplace/you/all')
    );

    if (sellingTab) {
      // Perfect! Already have a tab on the selling page
      console.log('Found existing selling tab:', sellingTab.id);
      targetTab = sellingTab;

      // Switch to this tab
      await chrome.tabs.update(sellingTab.id, { active: true });
      await chrome.windows.update(sellingTab.windowId, { focused: true });

      progressText.textContent = 'Switched to existing selling page...';
    } else if (fbTabs.length > 0) {
      // Have a Facebook tab, but not on selling page - navigate it there
      console.log('Navigating existing Facebook tab to selling page');
      targetTab = fbTabs[0];

      progressText.textContent = 'Navigating to selling page...';

      await chrome.tabs.update(targetTab.id, {
        url: correctUrl,
        active: true
      });
      await chrome.windows.update(targetTab.windowId, { focused: true });

      // Wait for navigation to complete
      await new Promise(resolve => setTimeout(resolve, 3000));
    } else {
      // No Facebook tabs at all - check current tab
      const [currentActiveTab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (currentActiveTab.url && currentActiveTab.url.includes('facebook.com')) {
        // Current tab is on Facebook - navigate it
        console.log('Navigating current Facebook tab to selling page');
        targetTab = currentActiveTab;

        progressText.textContent = 'Navigating to selling page...';

        await chrome.tabs.update(targetTab.id, { url: correctUrl });

        // Wait for navigation to complete
        await new Promise(resolve => setTimeout(resolve, 3000));
      } else {
        // No Facebook tabs - open a new one
        console.log('Opening new tab for Facebook Marketplace');

        progressText.textContent = 'Opening Facebook Marketplace...';

        targetTab = await chrome.tabs.create({
          url: correctUrl,
          active: true
        });

        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }

    currentTab = targetTab;
    progressText.textContent = 'Loading page content...';

    console.log('Target tab ID:', currentTab.id);
    console.log('Target tab URL:', currentTab.url);

    // Get admin token from storage
    const tokenResult = await chrome.storage.sync.get(['adminToken']);
    const adminToken = tokenResult.adminToken || 'changeme-super-secret-admin-api-bearer-moskva17';

    // Try to send message - if it fails, inject the content script
    let response;
    try {
      console.log('Sending extractListings message to tab', currentTab.id);
      progressText.textContent = 'Connecting to page...';

      response = await chrome.tabs.sendMessage(currentTab.id, {
        action: 'extractListings',
        skipScroll: skipScroll,
        apiUrl: apiUrl,
        adminToken: adminToken,
        enhanceLLM: enhanceLLM
      });
    } catch (error) {
      // Content script not loaded - inject it now
      console.log('Content script not found, injecting...', error.message);
      progressText.textContent = 'Injecting extension script...';

      try {
        // First, inject the content script
        await chrome.scripting.executeScript({
          target: { tabId: currentTab.id },
          files: ['scripts/content.js'],
          world: 'MAIN'
        });

        console.log('Content script injected successfully');

        // Wait longer for script to initialize and set up listeners
        await new Promise(resolve => setTimeout(resolve, 3000));

        progressText.textContent = 'Starting extraction...';

        // Try again with retry logic
        let retries = 3;
        let lastError = null;
        
        while (retries > 0) {
          try {
            response = await chrome.tabs.sendMessage(currentTab.id, {
              action: 'extractListings',
              skipScroll: skipScroll,
              apiUrl: apiUrl,
              adminToken: adminToken,
              enhanceLLM: enhanceLLM
            });
            break; // Success, exit retry loop
          } catch (msgError) {
            lastError = msgError;
            retries--;
            if (retries > 0) {
              console.log('Message send failed, retrying... (' + retries + ' attempts left)');
              await new Promise(resolve => setTimeout(resolve, 1500));
            }
          }
        }
        
        if (retries === 0 && lastError) {
          throw lastError;
        }
      } catch (injectError) {
        console.error('Failed to inject content script:', injectError);
        throw new Error('Failed to load extension on this page: ' + injectError.message + '. Try refreshing the Facebook page (F5) and try again.');
      }
    }

    console.log('Got response from content script:', response);

    if (!response) {
      throw new Error('No response from content script. The page may still be loading. Try waiting a moment and clicking Import again.');
    }

    if (!response.success) {
      throw new Error(response.error || 'Failed to process listings');
    }

    const result = response.result;

    // Content script did everything - just show results
    const imported = result.imported || 0;
    const updated = result.updated || 0;
    const skipped = result.skipped || 0;
    const errors = result.errors || 0;

    progressFill.style.width = '100%';
    progressText.textContent = 'Complete! Check log file in Downloads.';

    // Clear extraction state
    isExtracting = false;
    await chrome.storage.local.set({
      extractionState: {
        isActive: false,
        percent: 100,
        text: 'Complete!'
      }
    });

    setTimeout(() => {
      progress.style.display = 'none';
      controls.classList.remove('active');
      showStatus(
        'New: ' + imported + ' | Updated: ' + updated + ' | Skipped: ' + skipped + ' | Errors: ' + errors,
        errors > 0 ? 'info' : 'success'
      );
      btn.disabled = false;
      btn.textContent = 'Import ALL New Listings';
    }, 1000);

  } catch (error) {
    console.error('Import error:', error);
    showStatus('Error: ' + error.message, 'error');
    progress.style.display = 'none';
    controls.classList.remove('active');
    btn.disabled = false;
    btn.textContent = 'Import ALL New Listings';

    // Clear extraction state on error
    isExtracting = false;
    await chrome.storage.local.set({
      extractionState: {
        isActive: false
      }
    });
  }
});

// Pause button
document.getElementById('pauseBtn').addEventListener('click', async () => {
  const pauseBtn = document.getElementById('pauseBtn');

  if (!isPaused) {
    isPaused = true;
    pauseBtn.textContent = '▶️ Resume';
    pauseBtn.classList.remove('btn-warning');
    pauseBtn.classList.add('btn-primary');

    // Update state
    const state = await chrome.storage.local.get(['extractionState']);
    if (state.extractionState) {
      state.extractionState.isPaused = true;
      await chrome.storage.local.set({ extractionState: state.extractionState });
    }

    // Send pause message to content script
    if (currentTab) {
      try {
        await chrome.tabs.sendMessage(currentTab.id, { action: 'pause' });
      } catch (e) {
        // Ignore connection errors - content script may not be loaded yet
        if (!e.message.includes('Receiving end does not exist')) {
          console.error('Failed to send pause message:', e);
        }
      }
    }
  } else {
    isPaused = false;
    pauseBtn.textContent = '⏸️ Pause';
    pauseBtn.classList.remove('btn-primary');
    pauseBtn.classList.add('btn-warning');

    // Update state
    const state = await chrome.storage.local.get(['extractionState']);
    if (state.extractionState) {
      state.extractionState.isPaused = false;
      await chrome.storage.local.set({ extractionState: state.extractionState });
    }

    // Send resume message to content script
    if (currentTab) {
      try {
        await chrome.tabs.sendMessage(currentTab.id, { action: 'resume' });
      } catch (e) {
        // Ignore connection errors - content script may not be loaded yet
        if (!e.message.includes('Receiving end does not exist')) {
          console.error('Failed to send resume message:', e);
        }
      }
    }
  }
});

// Stop button
document.getElementById('stopBtn').addEventListener('click', async () => {
  isStopped = true;

  // Send stop message to content script
  if (currentTab) {
    try {
      await chrome.tabs.sendMessage(currentTab.id, { action: 'stop' });
    } catch (e) {
      // Ignore connection errors - content script may not be loaded yet
      if (!e.message.includes('Receiving end does not exist')) {
        console.error('Failed to send stop message:', e);
      }
    }
  }

  // Reset UI
  const btn = document.getElementById('importBtn');
  const controls = document.getElementById('controls');
  const progress = document.getElementById('progress');

  controls.classList.remove('active');
  btn.disabled = false;
  btn.textContent = 'Import ALL New Listings';

  // Clear extraction state
  isExtracting = false;
  await chrome.storage.local.set({
    extractionState: {
      isActive: false
    }
  });

  showStatus('Extraction stopped by user', 'info');
  setTimeout(() => {
    progress.style.display = 'none';
  }, 1000);
});

// Listen for progress updates from content script
chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
  if (request.action === 'updateProgress') {
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    if (request.percent !== undefined) {
      progressFill.style.width = request.percent + '%';
    }

    if (request.text) {
      progressText.textContent = request.text;
    }

    // Save progress to storage so it persists across popup close/open
    const state = await chrome.storage.local.get(['extractionState']);
    if (state.extractionState && state.extractionState.isActive) {
      state.extractionState.percent = request.percent;
      state.extractionState.text = request.text;
      await chrome.storage.local.set({ extractionState: state.extractionState });
    }
  }

  if (request.action === 'extractionComplete') {
    const controls = document.getElementById('controls');
    controls.classList.remove('active');

    // Clear extraction state
    isExtracting = false;
    await chrome.storage.local.set({
      extractionState: {
        isActive: false,
        percent: 100,
        text: 'Complete!'
      }
    });
  }
});

function showStatus(message, type) {
  const status = document.getElementById('status');
  status.textContent = message;
  status.className = 'status ' + type;
}

// Load saved API URL
chrome.storage.sync.get(['apiUrl'], result => {
  if (result.apiUrl) {
    document.getElementById('apiUrl').value = result.apiUrl;
  }
});

// Save API URL on change
document.getElementById('apiUrl').addEventListener('change', e => {
  chrome.storage.sync.set({ apiUrl: e.target.value });
});
