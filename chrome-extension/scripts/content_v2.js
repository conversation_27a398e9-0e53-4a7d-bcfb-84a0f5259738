/**
 * Content Script V2 - NEW APPROACH for Facebook's updated structure
 *
 * Facebook removed /item/ links from the selling page, so we need a different strategy:
 * 1. Find clickable listing containers (divs with role="button" or similar)
 * 2. Simulate clicks to open each listing in a new tab
 * 3. Extract data from the opened tab
 * 4. Close tab and move to next
 */

// Only log once on load
if (!window.oneOfStoreExtensionLoaded) {
  console.log('OneOf Store extension V2 loaded (click-based extraction)');
  window.oneOfStoreExtensionLoaded = true;
}

// Global state for pause/stop
let isPaused = false;
let isStopped = false;

// Helper function to send progress updates
function sendProgress(percent, text) {
  try {
    chrome.runtime.sendMessage({
      action: 'updateProgress',
      percent: percent,
      text: text
    });
  } catch (e) {
    // Popup might be closed, ignore
  }
}

// Global config from popup
let globalConfig = {
  apiUrl: 'https://oneofstore.com/api',
  adminToken: 'changeme-super-secret-admin-api-bearer-moskva17',
  enhanceLLM: false
};

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'pause') {
    isPaused = true;
    console.log('⏸️  PAUSED by user');
    sendResponse({ success: true });
    return true;
  }

  if (request.action === 'resume') {
    isPaused = false;
    console.log('▶️  RESUMED by user');
    sendResponse({ success: true });
    return true;
  }

  if (request.action === 'stop') {
    isStopped = true;
    console.log('⏹️  STOPPED by user');
    sendResponse({ success: true });
    return true;
  }

  if (request.action === 'extractListings') {
    const skipScroll = request.skipScroll || false;

    // Store config from popup
    globalConfig.apiUrl = request.apiUrl || 'https://oneofstore.com/api';
    globalConfig.adminToken = request.adminToken || 'changeme-super-secret-admin-api-bearer-moskva17';
    globalConfig.enhanceLLM = request.enhanceLLM || false;

    console.log('Starting NEW CLICK-BASED extraction...');
    console.log('API URL:', globalConfig.apiUrl);
    console.log('Enhance with LLM:', globalConfig.enhanceLLM);

    // Set a timeout
    const timeout = setTimeout(() => {
      console.error('Process timed out after 2 hours');
      sendResponse({ success: false, error: 'Process timed out after 2 hours.' });
    }, 120 * 60 * 1000);

    extractAndImportListingsV2(skipScroll)
      .then(result => {
        clearTimeout(timeout);
        console.log('Completed! Imported: ' + result.imported + ', Updated: ' + result.updated + ', Skipped: ' + result.skipped + ', Errors: ' + result.errors);
        sendResponse({ success: true, result });
      })
      .catch(error => {
        clearTimeout(timeout);
        console.error('Error during process:', error);
        sendResponse({ success: false, error: error.message });
      });

    return true; // Keep channel open for async response
  }
});

/**
 * NEW EXTRACTION METHOD - Find clickable listing cards
 */
async function extractAndImportListingsV2(skipScroll = false) {
  // Reset state
  isPaused = false;
  isStopped = false;

  const currentUrl = window.location.href;

  // Verify we're on the right page
  if (!currentUrl.includes('marketplace/you/selling') && !currentUrl.includes('marketplace/you/all')) {
    throw new Error('Please navigate to: Facebook Marketplace → "You" → "Selling"');
  }

  console.log('🔍 STEP 1: Finding listing cards on page...');
  sendProgress(5, 'Finding listings on page...');

  // Wait for page to load
  await waitForListingsToLoad();

  if (!skipScroll) {
    console.log('📜 STEP 2: Scrolling to load all listings...');
    sendProgress(10, 'Scrolling to load all listings...');
    try {
      await scrollToLoadAllItems();
    } catch (scrollError) {
      console.warn('Scrolling issue:', scrollError);
    }
  }

  // NEW APPROACH: Find all listing images and their clickable parents
  console.log('🎯 STEP 3: Finding clickable listing containers...');
  sendProgress(15, 'Analyzing page structure...');

  const listingImages = document.querySelectorAll('img[src*="scontent"]');
  console.log('Found ' + listingImages.length + ' listing images');

  const clickableListings = [];

  // For each image, find its clickable parent
  listingImages.forEach((img) => {
    let parent = img.parentElement;
    let depth = 0;

    while (parent && depth < 15) {
      const role = parent.getAttribute('role');
      const tabIndex = parent.getAttribute('tabindex');
      const ariaLabel = parent.getAttribute('aria-label') || '';

      // Look for clickable elements
      if ((role === 'link' || role === 'button' || tabIndex === '0' || tabIndex === '-1') &&
          parent.tagName !== 'BODY' && parent.tagName !== 'HTML') {

        // Avoid duplicates
        if (!clickableListings.includes(parent)) {
          clickableListings.push({
            element: parent,
            image: img.src,
            ariaLabel: ariaLabel
          });
        }
        break;
      }

      depth++;
      parent = parent.parentElement;
    }
  });

  console.log('📋 Found ' + clickableListings.length + ' clickable listing containers');

  if (clickableListings.length === 0) {
    throw new Error('No listings found on page. Make sure you have active listings and the page is fully loaded.');
  }

  // Log setup
  const extractionLog = [];
  const startTime = new Date();
  extractionLog.push('='.repeat(80));
  extractionLog.push('OneOf Store - Facebook Marketplace Import Log (Click Method)');
  extractionLog.push('Started: ' + startTime.toLocaleString());
  extractionLog.push('='.repeat(80));
  extractionLog.push('');

  let imported = 0;
  let updated = 0;
  let skipped = 0;
  let errors = 0;

  const maxListings = clickableListings.length;
  console.log('🚀 STEP 4: Processing ' + maxListings + ' listings...');

  // Process each listing by clicking it
  for (let i = 0; i < maxListings; i++) {
    // Check if stopped
    if (isStopped) {
      console.log('\n⏹️  STOPPED');
      break;
    }

    // Check if paused
    while (isPaused && !isStopped) {
      console.log('⏸️  PAUSED...');
      sendProgress(Math.floor((i / maxListings) * 100), 'Paused (' + i + '/' + maxListings + ')');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (isStopped) break;

    const listing = clickableListings[i];
    const percent = Math.floor((i / maxListings) * 100);

    try {
      sendProgress(percent, 'Processing listing ' + (i + 1) + '/' + maxListings);

      console.log('\n' + '='.repeat(80));
      console.log('📦 LISTING ' + (i + 1) + '/' + maxListings);
      console.log('='.repeat(80));

      // CLICK the listing to open it
      console.log('🖱️  Clicking listing...');
      listing.element.click();

      // Wait for page to navigate or modal to open
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Extract data from current page
      const listingData = extractListingFromCurrentPage();

      console.log('✅ Extracted:', listingData.title);
      console.log('   Price: $' + listingData.price);
      console.log('   Images: ' + listingData.gallery.length);

      // Import to VPS
      console.log('📤 Importing to VPS...');
      sendProgress(percent, 'Listing ' + (i + 1) + '/' + maxListings + ': Importing...');

      try {
        // Check if exists
        const checkResponse = await fetch(globalConfig.apiUrl + '/items');
        const existingItems = await checkResponse.json();
        const existingItem = existingItems.find(item => item.sku === 'FB-' + listingData.fbItemId);

        if (existingItem) {
          console.log('⏭️  Already exists, skipping');
          skipped++;
        } else {
          // Import
          const item = {
            sku: 'FB-' + listingData.fbItemId,
            title: listingData.title,
            description: listingData.description || listingData.title,
            price: listingData.price || 0,
            cover_url: listingData.imageUrl || '',
            gallery: listingData.gallery || [],
            category: 'Other',
            tags: ['facebook-marketplace'],
            condition: listingData.condition || 'used',
            fb_url: listingData.url
          };

          const importResponse = await fetch(globalConfig.apiUrl + '/items', {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer ' + globalConfig.adminToken,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(item)
          });

          if (!importResponse.ok) {
            throw new Error('HTTP ' + importResponse.status);
          }

          const result = await importResponse.json();
          console.log('✅ IMPORTED! VPS ID:', result.id);
          imported++;
        }
      } catch (importErr) {
        console.error('❌ Import failed:', importErr.message);
        errors++;
      }

      // Go back to listings page
      console.log('◀️  Going back...');
      window.history.back();
      await new Promise(resolve => setTimeout(resolve, 1500));

    } catch (err) {
      console.error('❌ Error processing listing ' + (i + 1) + ':', err);
      errors++;

      // Try to go back if we navigated away
      if (window.location.href !== currentUrl) {
        window.history.back();
        await new Promise(resolve => setTimeout(resolve, 1500));
      }
    }
  }

  console.log('\n✅ COMPLETE!');
  console.log('New: ' + imported + ' | Updated: ' + updated + ' | Skipped: ' + skipped + ' | Errors: ' + errors);

  sendProgress(100, 'Complete! New: ' + imported + ', Skipped: ' + skipped);

  return { imported, updated, skipped, errors, total: imported + updated + skipped + errors };
}

/**
 * Extract listing data from the current page (after clicking)
 */
function extractListingFromCurrentPage() {
  const listing = {
    fbItemId: Date.now().toString(), // Fallback
    title: '',
    price: 0,
    description: '',
    imageUrl: null,
    gallery: [],
    condition: 'used',
    url: window.location.href
  };

  // Extract item ID from URL
  const itemIdMatch = window.location.href.match(/\/item\/(\d+)/);
  if (itemIdMatch) {
    listing.fbItemId = itemIdMatch[1];
  }

  // Extract title
  const h1 = document.querySelector('h1');
  if (h1) {
    listing.title = h1.textContent.trim();
  }

  // Extract price
  const bodyText = document.body.textContent;
  const priceMatch = bodyText.match(/\$([0-9,]+(?:\.\d{2})?)/);
  if (priceMatch) {
    listing.price = parseFloat(priceMatch[1].replace(/,/g, ''));
  }

  // Extract images
  const images = document.querySelectorAll('img[src*="scontent"]');
  const imageUrls = new Set();
  images.forEach(img => {
    if (img.src && !img.src.includes('emoji') && !img.src.includes('profile')) {
      imageUrls.add(img.src);
    }
  });
  listing.gallery = Array.from(imageUrls);
  listing.imageUrl = listing.gallery[0] || null;

  // Extract description
  const spans = document.querySelectorAll('span[dir="auto"]');
  const textParts = [];
  spans.forEach(span => {
    const text = span.textContent.trim();
    if (text.length > 30 && text.length < 5000 && !textParts.includes(text)) {
      textParts.push(text);
    }
  });
  listing.description = textParts.slice(0, 3).join('\n\n') || listing.title;

  return listing;
}

/**
 * Wait for listings to load
 */
async function waitForListingsToLoad() {
  const maxWaitTime = 30000;
  const checkInterval = 500;
  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitTime) {
    const images = document.querySelectorAll('img[src*="scontent"]');
    if (images.length > 0) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      return;
    }
    await new Promise(resolve => setTimeout(resolve, checkInterval));
  }

  console.warn('Timeout waiting for listings');
}

/**
 * Scroll to load all items
 */
async function scrollToLoadAllItems() {
  let lastHeight = document.body.scrollHeight;
  let unchangedCount = 0;
  const maxAttempts = 3;
  let scrollCount = 0;

  while (unchangedCount < maxAttempts && scrollCount < 20) {
    scrollCount++;
    window.scrollTo(0, document.body.scrollHeight);
    await new Promise(resolve => setTimeout(resolve, 1000));

    const newHeight = document.body.scrollHeight;
    if (newHeight === lastHeight) {
      unchangedCount++;
    } else {
      unchangedCount = 0;
    }
    lastHeight = newHeight;
  }

  window.scrollTo(0, 0);
  await new Promise(resolve => setTimeout(resolve, 300));
}
