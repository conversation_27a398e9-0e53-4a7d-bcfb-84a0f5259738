/**
 * Diagnostic script to check if you're on the right Facebook page
 * Paste this into browser console (F12) while on Facebook Marketplace
 */

console.log('='.repeat(60));
console.log('FACEBOOK MARKETPLACE DIAGNOSTIC');
console.log('='.repeat(60));

const url = window.location.href;
console.log('\n📍 Current URL:', url);
console.log('📄 Page Title:', document.title);

console.log('\n' + '-'.repeat(60));
console.log('PAGE DETECTION');
console.log('-'.repeat(60));

if (url.includes('marketplace/you/selling')) {
  console.log('✅ You are on the SELLING page - CORRECT!');
} else if (url.includes('marketplace/you/all')) {
  console.log('✅ You are on the ALL page - CORRECT!');
} else if (url.includes('marketplace/you/shipping_orders')) {
  console.log('❌ You are on the SHIPPING ORDERS page - WRONG!');
  console.log('   👉 Click "Selling" in the left sidebar');
} else if (url.includes('marketplace/you/sold')) {
  console.log('❌ You are on the SOLD page - WRONG!');
  console.log('   👉 Click "Selling" in the left sidebar');
} else if (url.includes('marketplace/you/archived')) {
  console.log('❌ You are on the ARCHIVED page - WRONG!');
  console.log('   👉 Click "Selling" in the left sidebar');
} else if (url.includes('marketplace')) {
  console.log('⚠️  You are on Marketplace but not in the "You" section');
  console.log('   👉 Click "You" at the top, then "Selling" in sidebar');
} else {
  console.log('❌ You are NOT on Facebook Marketplace');
  console.log('   👉 Go to facebook.com/marketplace');
}

console.log('\n' + '-'.repeat(60));
console.log('LINK DETECTION');
console.log('-'.repeat(60));

const totalLinks = document.querySelectorAll('a').length;
const itemLinks = document.querySelectorAll('a[href*="/item/"]').length;
const itemLinksAll = Array.from(document.querySelectorAll('a')).filter(a => {
  const href = a.getAttribute('href') || '';
  return href.includes('/item/');
}).length;

console.log('Total <a> tags on page:', totalLinks);
console.log('Links with /item/ in href:', itemLinks);
console.log('Links with /item/ (manual filter):', itemLinksAll);

if (itemLinks > 0) {
  console.log('✅ FOUND clickable listing links! Extension should work.');

  // Show sample
  const sampleLink = document.querySelector('a[href*="/item/"]');
  if (sampleLink) {
    console.log('\n📋 Sample listing link:');
    console.log('   href:', sampleLink.getAttribute('href'));
    const itemId = sampleLink.getAttribute('href').match(/item\/(\d+)/);
    if (itemId) {
      console.log('   Item ID:', itemId[1]);
    }
  }
} else {
  console.log('❌ NO clickable listing links found!');
  console.log('   This means you are on the wrong page.');
  console.log('   👉 Go to: Marketplace → You → Selling');
}

console.log('\n' + '-'.repeat(60));
console.log('LISTING CARDS DETECTION');
console.log('-'.repeat(60));

const ariaLabels = document.querySelectorAll('[aria-label]');
const listingCards = Array.from(ariaLabels).filter(el => {
  const label = el.getAttribute('aria-label') || '';
  return label.length > 10 &&
         !label.includes('Mark as sold') &&
         !label.includes('Share') &&
         !label.includes('More options');
});

console.log('Total aria-label elements:', ariaLabels.length);
console.log('Potential listing cards:', listingCards.length);

if (listingCards.length > 0) {
  console.log('\n📋 Sample listing titles:');
  listingCards.slice(0, 5).forEach((el, i) => {
    const label = el.getAttribute('aria-label');
    console.log('   ' + (i + 1) + '. ' + label.substring(0, 60) + '...');
  });
}

console.log('\n' + '='.repeat(60));
console.log('RECOMMENDATION');
console.log('='.repeat(60));

if (itemLinks > 0) {
  console.log('✅ You are on the correct page! The extension should work.');
  console.log('   Click the extension icon and try "Import ALL New Listings"');
} else if (listingCards.length > 0 && itemLinks === 0) {
  console.log('❌ You can SEE listings but they are NOT clickable.');
  console.log('   This happens on Shipping Orders, Sold, or Archive pages.');
  console.log('\n   SOLUTION:');
  console.log('   1. Click "Selling" in the LEFT sidebar');
  console.log('   2. Run this diagnostic again to verify');
  console.log('   3. Then use the extension');
} else {
  console.log('❌ No listings found at all.');
  console.log('\n   SOLUTION:');
  console.log('   1. Go to Facebook Marketplace');
  console.log('   2. Click "You" at the top');
  console.log('   3. Click "Selling" in the left sidebar');
  console.log('   4. Scroll down to load some listings');
  console.log('   5. Run this diagnostic again');
}

console.log('\n' + '='.repeat(60));
