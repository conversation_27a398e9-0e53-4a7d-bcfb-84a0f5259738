/**
 * Background Service Worker
 * Handles message routing between popup and content scripts
 */

// Keep service worker alive during long operations
let keepAliveInterval = null;

function startKeepAlive() {
  if (keepAliveInterval) return;

  console.log('[Background] Starting keep-alive...');
  keepAliveInterval = setInterval(() => {
    console.log('[Background] Keep-alive ping');
  }, 20000); // Ping every 20 seconds to prevent termination
}

function stopKeepAlive() {
  if (keepAliveInterval) {
    console.log('[Background] Stopping keep-alive');
    clearInterval(keepAliveInterval);
    keepAliveInterval = null;
  }
}

// Listen for messages from popup and content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('[Background] Received message:', request.action, 'from tab:', sender.tab?.id);

  // Route progress updates from content script to popup
  if (request.action === 'updateProgress') {
    // Broadcast to all popup windows
    chrome.runtime.sendMessage({
      action: 'updateProgress',
      percent: request.percent,
      text: request.text
    }).catch(() => {
      // Popup might not be open, ignore
    });
    sendResponse({ success: true });
    return true;
  }

  // Route extraction complete from content script to popup
  if (request.action === 'extractionComplete') {
    chrome.runtime.sendMessage({
      action: 'extractionComplete'
    }).catch(() => {
      // Popup might not be open, ignore
    });
    sendResponse({ success: true });
    return true;
  }

  // Handle pause/resume/stop from popup to content script
  if (request.action === 'pause' || request.action === 'resume' || request.action === 'stop') {
    // Get the active tab and forward the message
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, request).catch(() => {
          console.log('[Background] Could not send message to content script');
        });
      }
    });
    sendResponse({ success: true });
    return true;
  }

  // Start keep-alive for long operations
  if (request.action === 'startKeepAlive') {
    startKeepAlive();
    sendResponse({ success: true });
    return true;
  }

  // Stop keep-alive
  if (request.action === 'stopKeepAlive') {
    stopKeepAlive();
    sendResponse({ success: true });
    return true;
  }

  // Handle opening new tab and extracting data
  if (request.action === 'openTabAndExtract') {
    const { url, itemId } = request;
    console.log('[Background] Opening new tab for:', url);

    // Ensure keep-alive is running
    startKeepAlive();

    // Open new tab in background
    chrome.tabs.create({ url: url, active: false }, async (newTab) => {
      try {
        console.log('[Background] New tab created:', newTab.id);

        // Wait for tab to finish loading
        await new Promise((resolve) => {
          const listener = (tabId, changeInfo) => {
            if (tabId === newTab.id && changeInfo.status === 'complete') {
              chrome.tabs.onUpdated.removeListener(listener);
              console.log('[Background] Tab loaded:', newTab.id);
              resolve();
            }
          };
          chrome.tabs.onUpdated.addListener(listener);

          // Timeout after 15 seconds
          setTimeout(() => {
            chrome.tabs.onUpdated.removeListener(listener);
            resolve();
          }, 15000);
        });

        // Wait extra time for content to render
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Send extraction message to new tab
        console.log('[Background] Sending extraction message to tab:', newTab.id);
        chrome.tabs.sendMessage(newTab.id, {
          action: 'extractCurrentPage',
          url: url,
          itemId: itemId
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('[Background] Error:', chrome.runtime.lastError.message);
            sendResponse({
              success: false,
              error: chrome.runtime.lastError.message,
              tabId: newTab.id
            });
          } else if (!response) {
            console.error('[Background] No response from content script');
            sendResponse({
              success: false,
              error: 'No response from content script',
              tabId: newTab.id
            });
          } else {
            console.log('[Background] Extraction successful');
            sendResponse({
              success: true,
              listing: response.listing,
              tabId: newTab.id
            });
          }
        });
      } catch (err) {
        console.error('[Background] Error opening tab:', err);
        sendResponse({
          success: false,
          error: err.message,
          tabId: newTab.id
        });
      }
    });

    return true; // Keep channel open for async response
  }

  // Handle closing a tab
  if (request.action === 'closeTab') {
    const { tabId } = request;
    console.log('[Background] Closing tab:', tabId);

    chrome.tabs.remove(tabId, () => {
      if (chrome.runtime.lastError) {
        console.error('[Background] Error closing tab:', chrome.runtime.lastError.message);
        sendResponse({ success: false, error: chrome.runtime.lastError.message });
      } else {
        console.log('[Background] Tab closed:', tabId);
        sendResponse({ success: true });
      }
    });

    return true;
  }

  // Proxy API requests to avoid CORS issues
  if (request.action === 'apiRequest') {
    const { method, url, headers, body } = request;
    console.log('[Background] Proxying API request:', method, url);
    if (body) {
      console.log('[Background] Request body:', JSON.stringify(body, null, 2));
    }
    if (headers) {
      console.log('[Background] Request headers:', headers);
    }

    fetch(url, {
      method: method,
      headers: headers || {},
      body: body ? JSON.stringify(body) : undefined
    })
      .then(response => {
        console.log('[Background] API response:', response.status, response.statusText);

        // Get response text first
        return response.text().then(text => {
          console.log('[Background] Response text:', text.substring(0, 1000)); // Log first 1000 chars

          // Try to parse as JSON
          let data;
          try {
            data = text ? JSON.parse(text) : null;
          } catch (e) {
            console.warn('[Background] Could not parse response as JSON:', e.message);
            data = text;
          }

          return {
            ok: response.ok,
            status: response.status,
            statusText: response.statusText,
            data: data
          };
        });
      })
      .then(result => {
        if (!result.ok) {
          console.error('[Background] API error:', result.status, result.statusText, result.data);
        }
        sendResponse({ success: true, response: result });
      })
      .catch(error => {
        console.error('[Background] API request failed:', error);
        sendResponse({ success: false, error: error.message });
      });

    return true; // Keep channel open for async response
  }

  sendResponse({ success: false, error: 'Unknown action' });
});

// Handle tab updates to ensure content script is ready
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('facebook.com/marketplace')) {
    console.log('[Background] Facebook Marketplace page loaded:', tab.url);
  }
});
