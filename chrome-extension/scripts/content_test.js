/**
 * MINIMAL TEST CONTENT SCRIPT
 * Just to verify content scripts CAN load on Facebook
 */

console.log('='.repeat(80));
console.log('TEST CONTENT SCRIPT LOADED!');
console.log('Current URL:', window.location.href);
console.log('='.repeat(80));

// Set up message listener
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('TEST: Received message:', request.action);
  sendResponse({ success: true, message: 'Test content script is working!' });
  return true;
});

window.testContentScriptLoaded = true;
