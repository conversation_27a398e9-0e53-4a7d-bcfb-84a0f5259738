/**
 * DEBUG SCRIPT - Extract share links by clicking Share buttons
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Extract via Share Buttons');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

// Find all Share buttons
console.log('🔍 Looking for Share buttons...');

const possibleShareSelectors = [
  'div[aria-label="Share"]',
  'span:contains("Share")',
  '[role="button"]:contains("Share")',
  'div:contains("Share")',
  'a[aria-label="Share"]',
  'button:contains("Share")'
];

// Try to find Share buttons
let shareButtons = [];

// Method 1: Look for aria-label="Share"
shareButtons = Array.from(document.querySelectorAll('div[aria-label="Share"], span[aria-label="Share"], [aria-label="Share"]'));
console.log(`  aria-label="Share": ${shareButtons.length} found`);

// Method 2: If not found, search all clickable elements for "Share" text
if (shareButtons.length === 0) {
  console.log('  Searching all elements for "Share" text...');
  const allClickable = document.querySelectorAll('div[role="button"], span, a, button');
  shareButtons = Array.from(allClickable).filter(el => {
    const text = el.textContent.trim();
    return text === 'Share' || text === 'share';
  });
  console.log(`  Text search: ${shareButtons.length} found`);
}

if (shareButtons.length === 0) {
  console.log('❌ No Share buttons found!');
  console.log('\nLet me show you all buttons on the page:');

  const allButtons = document.querySelectorAll('[role="button"], button');
  console.log(`\nTotal buttons: ${allButtons.length}`);
  console.log('\nFirst 20 button labels:');

  Array.from(allButtons).slice(0, 20).forEach((btn, idx) => {
    const label = btn.getAttribute('aria-label') || btn.textContent.trim().substring(0, 30) || '(no label)';
    console.log(`  ${idx + 1}. ${label}`);
  });

  console.log('\n❌ Cannot proceed without Share buttons');
  console.log('Please scroll down to see your listings, then run again');
} else {
  console.log(`\n✅ Found ${shareButtons.length} Share buttons!\n`);

  // Function to wait
  function wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Function to close dialogs
  function closeDialogs() {
    // Press Escape
    document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', keyCode: 27 }));
    // Click outside
    const backdrop = document.querySelector('[role="dialog"]');
    if (backdrop) {
      backdrop.parentElement.click();
    }
  }

  // Extract share links
  async function extractShareLinks() {
    const shareLinks = [];
    const maxToTest = Math.min(shareButtons.length, 3); // Test first 3

    console.log(`Testing first ${maxToTest} listings...\n`);

    for (let i = 0; i < maxToTest; i++) {
      const button = shareButtons[i];

      console.log(`📦 Listing ${i + 1}/${maxToTest}`);

      try {
        // Close any open dialogs
        closeDialogs();
        await wait(300);

        // Scroll button into view
        button.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await wait(500);

        console.log('  Clicking Share button...');
        button.click();
        await wait(1500); // Wait for share dialog to appear

        console.log('  Share dialog opened, looking for Copy link...');

        // Look for the share dialog
        const dialog = document.querySelector('[role="dialog"]');
        if (!dialog) {
          console.log('  ⚠️  No dialog appeared');
          continue;
        }

        // Look for "Copy link" in the dialog
        const allElements = dialog.querySelectorAll('div, span, a, [role="button"]');

        let foundLink = null;
        for (const el of allElements) {
          const text = el.textContent || '';
          const href = el.getAttribute('href') || '';

          // Look for "Copy link" text
          if (text.includes('Copy link') || text.includes('copy link')) {
            console.log(`    Found "Copy link" element`);

            // Check if this element or parent has a share URL
            if (href.includes('/share/')) {
              foundLink = href;
              console.log(`    ✅ Found share URL in href: ${href}`);
              break;
            }

            // Check parent/siblings for link
            const parent = el.parentElement;
            const linkInParent = parent?.querySelector('a[href*="/share/"]');
            if (linkInParent) {
              foundLink = linkInParent.getAttribute('href');
              console.log(`    ✅ Found share URL in parent: ${foundLink}`);
              break;
            }

            // Try clicking the Copy link button to get URL from clipboard
            console.log('    Trying to click Copy link...');
            el.click();
            await wait(500);

            // Try to read from clipboard (may not work due to permissions)
            try {
              const clipboardText = await navigator.clipboard.readText();
              if (clipboardText.includes('/share/') || clipboardText.includes('facebook.com')) {
                foundLink = clipboardText;
                console.log(`    ✅ Got URL from clipboard: ${foundLink}`);
                break;
              }
            } catch (clipErr) {
              console.log(`    ⚠️  Cannot read clipboard: ${clipErr.message}`);
            }
          }
        }

        // Also look for any /share/ links in the dialog
        if (!foundLink) {
          console.log('    Searching for /share/ links in dialog...');
          const shareLinksInDialog = dialog.querySelectorAll('a[href*="/share/"]');
          if (shareLinksInDialog.length > 0) {
            foundLink = shareLinksInDialog[0].getAttribute('href');
            console.log(`    ✅ Found share link: ${foundLink}`);
          }
        }

        if (foundLink) {
          // Clean up URL
          if (foundLink.startsWith('/')) {
            foundLink = 'https://www.facebook.com' + foundLink;
          }
          foundLink = foundLink.split('?')[0];

          shareLinks.push(foundLink);
          console.log(`  ✅ Extracted: ${foundLink}\n`);
        } else {
          console.log('  ❌ Could not find share URL\n');
        }

        // Close dialog
        closeDialogs();
        await wait(500);

      } catch (err) {
        console.error(`  ❌ Error: ${err.message}\n`);
        closeDialogs();
      }
    }

    // Final results
    console.log('='.repeat(80));
    console.log('📊 EXTRACTION RESULTS');
    console.log('='.repeat(80));
    console.log(`Total share links found: ${shareLinks.length}`);

    if (shareLinks.length > 0) {
      console.log('\nExtracted URLs:');
      shareLinks.forEach((url, idx) => {
        console.log(`  ${idx + 1}. ${url}`);
      });

      console.log('\n✅ SUCCESS!');
      console.log('\nAll ' + shareButtons.length + ' listings can be extracted this way.');
      console.log('Extension will click Share → Copy link for each listing.');
    } else {
      console.log('\n❌ No share links extracted');
      console.log('The share dialog may have a different structure');
    }

    console.log('='.repeat(80));

    return shareLinks;
  }

  // Run extraction
  console.log('⚡ Starting automated extraction in 2 seconds...');
  console.log('(Watch your screen - Share dialogs will open/close)\n');

  setTimeout(() => {
    extractShareLinks().then(links => {
      console.log('\n✅ Complete!');
      window.extractedShareLinks = links;
      console.log('Links saved to: window.extractedShareLinks');
    });
  }, 2000);
}
