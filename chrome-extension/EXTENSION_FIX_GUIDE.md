# Chrome Extension Fix Guide

## Issue: "Could not establish connection. Receiving end does not exist"

This error occurs when the content script cannot communicate with the popup or background script. The fixes applied address the root causes.

## What Was Fixed

### 1. **Manifest Configuration (manifest.json)**
- Added `background` service worker for proper message routing
- Added `web_accessible_resources` for better script isolation
- Ensured proper `host_permissions` for Facebook domains
- Added `activeTab` permission for tab access

### 2. **Content Script Initialization (scripts/content.js)**
- Added robust message listener setup with error handling
- Implemented connection verification
- Added fallback message handlers
- Improved error logging for debugging

### 3. **Background Service Worker (scripts/background.js)**
- Created new background worker to relay messages between popup and content script
- Handles message routing and error cases
- Provides fallback communication paths

### 4. **Popup Script Improvements (scripts/popup.js)**
- Enhanced script injection with better error handling
- Added retry logic for failed injections
- Improved connection verification
- Better error messages for users

## How to Test the Fix

### Step 1: Reload the Extension
1. Open Chrome DevTools (F12)
2. Go to `chrome://extensions/`
3. Find "OneOf Store - Facebook Marketplace Importer"
4. Click the **Reload** button (circular arrow icon)

### Step 2: Verify Content Script is Loaded
1. Go to Facebook Marketplace (https://www.facebook.com/marketplace/you/selling)
2. Open Chrome DevTools (F12)
3. Go to **Console** tab
4. You should see: `✅ OneOf Store extension loaded`
5. If you don't see this, the content script didn't load

### Step 3: Test the Extension
1. Click the extension icon in the top-right corner
2. Enter your API URL
3. Click "Import ALL New Listings"
4. Monitor the console for messages

## Troubleshooting Steps

### If you still see "Receiving end does not exist":

**Option 1: Refresh the Facebook Page**
- Press F5 to refresh the Facebook Marketplace page
- Wait 2-3 seconds for the page to fully load
- Try the import again

**Option 2: Check Console for Errors**
1. Open DevTools (F12) on the Facebook page
2. Go to **Console** tab
3. Look for red error messages
4. Take a screenshot and share the errors

**Option 3: Verify Extension is Loaded**
1. Open DevTools on Facebook page
2. Go to **Sources** tab
3. Look for `content.js` in the left panel under "Content Scripts"
4. If it's not there, the extension didn't inject properly

**Option 4: Hard Reload Extension**
1. Go to `chrome://extensions/`
2. Toggle the extension OFF
3. Wait 2 seconds
4. Toggle it back ON
5. Refresh the Facebook page
6. Try again

### If script injection fails:

The popup will now show a more detailed error message. Common causes:

1. **Facebook page not fully loaded** - Wait a few seconds and try again
2. **Wrong URL** - Make sure you're on `facebook.com/marketplace/you/selling`
3. **Extension permissions** - The extension may need to be re-installed
4. **Browser cache** - Try clearing browser cache or using incognito mode

## Debug Mode

To enable detailed logging:

1. Open DevTools on the Facebook page (F12)
2. Go to **Console** tab
3. Paste this command:
```javascript
window.DEBUG_EXTENSION = true;
```
4. Try the import again
5. All debug messages will appear in the console

## Files Modified

- `manifest.json` - Added background worker and permissions
- `scripts/content.js` - Improved initialization and error handling
- `scripts/background.js` - NEW: Message routing service worker
- `scripts/popup.js` - Enhanced injection and error handling

## Key Changes Summary

| File | Change | Purpose |
|------|--------|---------|
| manifest.json | Added `background` service worker | Enable message routing |
| manifest.json | Added `web_accessible_resources` | Better script isolation |
| content.js | Added connection verification | Ensure script is ready |
| content.js | Improved error handling | Better debugging |
| background.js | NEW file | Route messages between popup and content |
| popup.js | Enhanced injection logic | Retry on failure |
| popup.js | Better error messages | User-friendly feedback |

## If Issues Persist

1. **Check Chrome version** - Must be Chrome 88+
2. **Disable other extensions** - Other extensions may interfere
3. **Try incognito mode** - Some extensions block in normal mode
4. **Reinstall extension** - Remove and re-add the extension
5. **Check Facebook permissions** - Make sure extension has permission for facebook.com

## Contact Support

If you continue to experience issues:
1. Open DevTools (F12)
2. Go to Console tab
3. Right-click and select "Save as..." to save console output
4. Share the console output with support
