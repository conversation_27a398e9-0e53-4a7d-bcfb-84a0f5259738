/**
 * DEBUG SCRIPT - Extract share URL from WhatsApp link in Share dialog
 * SOLUTION: The share URL is encoded in the WhatsApp share link!
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Extract via WhatsApp Link');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function closeDialogs() {
  // Press Escape multiple times
  for (let i = 0; i < 3; i++) {
    document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', keyCode: 27 }));
  }
  // Click outside dialog
  const backdrop = document.querySelector('[role="dialog"]');
  if (backdrop) {
    const parent = backdrop.parentElement;
    if (parent) {
      parent.click();
    }
  }
  // Click body to ensure focus is reset
  document.body.click();
}

// Find Share buttons
console.log('🔍 Finding Share buttons...');
let shareButtons = Array.from(document.querySelectorAll('[aria-label*="Share"]'))
  .filter(btn => {
    const label = btn.getAttribute('aria-label');
    return label && label.startsWith('Share ') && !label.includes('More options');
  });

console.log(`✅ Found ${shareButtons.length} Share buttons\n`);

if (shareButtons.length === 0) {
  console.log('❌ No Share buttons found. Scroll down and try again.');
} else {
  async function extractShareUrls() {
    const maxToTest = Math.min(shareButtons.length, 3); // Only test 3 to avoid Facebook getting stuck
    console.log(`🧪 Extracting share URLs from first ${maxToTest} listings...\n`);

    const results = [];

    for (let i = 0; i < maxToTest; i++) {
      const shareBtn = shareButtons[i];
      const ariaLabel = shareBtn.getAttribute('aria-label');
      const titleMatch = ariaLabel.match(/Share (.+)/);
      const title = titleMatch ? titleMatch[1] : 'Unknown';

      console.log(`📦 ${i + 1}/${maxToTest}: "${title.substring(0, 50)}"`);

      try {
        // FULLY close any open dialogs and reset
        closeDialogs();
        await wait(800);

        // Verify dialog is closed
        let dialogCheck = document.querySelector('[role="dialog"]');
        if (dialogCheck) {
          console.log(`  ⚠️ Dialog still open, closing again...`);
          closeDialogs();
          await wait(800);
        }

        // Scroll into view
        shareBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await wait(500);

        // Click Share button
        shareBtn.click();
        await wait(2000); // Wait longer for dialog to fully load

        // Find WhatsApp link
        const whatsappLink = document.querySelector('a[href*="wa.me"]');

        if (!whatsappLink) {
          console.log(`  ❌ No WhatsApp link found\n`);
          continue;
        }

        const href = whatsappLink.getAttribute('href');
        console.log(`  ✅ Found WhatsApp link`);

        // Decode URL to extract share URL
        // Format: https://l.facebook.com/l.php?u=https%3A%2F%2Fwa.me%2F%3Ftext%3Dhttps%253A%252F%252Fwww.facebook.com%252Fshare%252FXXXXX%252F

        // Extract the 'u' parameter
        const urlMatch = href.match(/[?&]u=([^&]+)/);
        if (!urlMatch) {
          console.log(`  ❌ Could not find 'u' parameter\n`);
          continue;
        }

        // Decode it once
        const decoded1 = decodeURIComponent(urlMatch[1]);
        // Should be: https://wa.me/?text=https%3A%2F%2Fwww.facebook.com%2Fshare%2FXXXXX%2F

        // Extract the 'text' parameter
        const textMatch = decoded1.match(/[?&]text=([^&]+)/);
        if (!textMatch) {
          console.log(`  ❌ Could not find 'text' parameter\n`);
          continue;
        }

        // Decode it again to get the actual share URL
        const shareUrl = decodeURIComponent(textMatch[1]);
        // Should be: https://www.facebook.com/share/XXXXX/

        // Extract share ID
        const shareIdMatch = shareUrl.match(/\/share\/([a-zA-Z0-9]+)/);
        const shareId = shareIdMatch ? shareIdMatch[1] : null;

        if (shareId) {
          console.log(`  🎉 Share URL: ${shareUrl}`);
          console.log(`  🎉 Share ID: ${shareId}\n`);

          results.push({
            title: title,
            shareUrl: shareUrl,
            shareId: shareId
          });
        } else {
          console.log(`  ⚠️  Decoded URL but no share ID found: ${shareUrl}\n`);
        }

        // FULLY close dialog and reset for next item
        closeDialogs();
        await wait(1000); // Wait longer to ensure complete reset

        // Verify closed
        dialogCheck = document.querySelector('[role="dialog"]');
        if (dialogCheck) {
          console.log(`  ⚠️ Dialog still open after extraction, forcing close...`);
          closeDialogs();
          await wait(1000);
        }

      } catch (err) {
        console.error(`  ❌ Error: ${err.message}\n`);
        closeDialogs();
        await wait(1000);
      }
    }

    // Final summary
    console.log('='.repeat(80));
    console.log('📊 EXTRACTION COMPLETE');
    console.log('='.repeat(80));
    console.log('');

    if (results.length > 0) {
      console.log(`✅ Successfully extracted ${results.length} share URLs!`);
      console.log('');

      results.forEach((item, idx) => {
        console.log(`${idx + 1}. "${item.title.substring(0, 50)}"`);
        console.log(`   ${item.shareUrl}`);
        console.log('');
      });

      window.extractedShareUrls = results;
      console.log('Saved to: window.extractedShareUrls');
      console.log('');

      console.log('🎉🎉🎉 SUCCESS! 🎉🎉🎉');
      console.log('');
      console.log('This method works!');
      console.log('Extension can:');
      console.log('  1. Click Share button for each listing');
      console.log('  2. Find WhatsApp link in dialog');
      console.log('  3. Decode URL to get share URL');
      console.log('  4. Navigate to share URL');
      console.log('  5. Extract all product data');
      console.log('  6. Import to OneOfStore');

    } else {
      console.log('❌ No share URLs extracted');
    }

    console.log('='.repeat(80));
  }

  // Run extraction
  console.log('⏳ Starting extraction in 2 seconds...\n');
  setTimeout(() => {
    extractShareUrls().then(() => {
      console.log('\n✅ Test complete!');
    });
  }, 2000);
}
