/**
 * DEBUG SCRIPT - Run this in the browser console on your Facebook Marketplace selling page
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Debug Link Detection');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

const allLinks = document.querySelectorAll('a');
console.log('Total links:', allLinks.length);
console.log('');

// Method 1: /share/ links (Facebook's NEW format!)
console.log('METHOD 1: /share/ links (NEW FORMAT)');
const shareLinks = [];
allLinks.forEach(link => {
  const href = link.getAttribute('href');
  if (href && href.includes('/share/')) {
    const match = href.match(/\/share\/([^\/\?]+)/);
    if (match) shareLinks.push(match[1]);
  }
});
console.log('Found:', shareLinks.length);
if (shareLinks.length > 0) {
  console.log('Sample IDs:', shareLinks.slice(0, 3));
  console.log('Sample URLs:', shareLinks.slice(0, 3).map(id => 'https://www.facebook.com/share/' + id));
}

console.log('');

// Method 2: Edit links (listing_id=)
console.log('METHOD 2: Edit links (listing_id=)');
const editLinks = [];
allLinks.forEach(link => {
  const href = link.getAttribute('href');
  if (href && href.includes('listing_id=')) {
    const match = href.match(/listing_id=(\d+)/);
    if (match) editLinks.push(match[1]);
  }
});
console.log('Found:', editLinks.length);
if (editLinks.length > 0) console.log('Sample:', editLinks.slice(0, 3));

console.log('');

// Method 3: /item/ links (OLD format)
console.log('METHOD 3: /item/ links (OLD FORMAT)');
const itemLinks = [];
allLinks.forEach(link => {
  const href = link.getAttribute('href');
  if (href && href.includes('/item/')) {
    const match = href.match(/\/item\/(\d+)/);
    if (match) itemLinks.push(match[1]);
  }
});
console.log('Found:', itemLinks.length);
if (itemLinks.length > 0) console.log('Sample:', itemLinks.slice(0, 3));

console.log('');

// Method 4: HTML source search
console.log('METHOD 4: HTML source search');
const html = document.documentElement.outerHTML;
const htmlMatches = html.match(/listing[_\-\/]id[=:]["']?(\d{15,20})/gi);
const htmlIds = new Set();
if (htmlMatches) {
  htmlMatches.forEach(m => {
    const id = m.match(/(\d{15,20})/);
    if (id) htmlIds.add(id[1]);
  });
}
console.log('Found:', htmlIds.size);
if (htmlIds.size > 0) console.log('Sample:', Array.from(htmlIds).slice(0, 3));

console.log('');

// Total
const allShareIds = shareLinks;
const allNumericIds = new Set([...editLinks, ...itemLinks, ...Array.from(htmlIds)]);
const totalUrls = allShareIds.length + allNumericIds.size;

console.log('='.repeat(80));
console.log('TOTAL UNIQUE LISTINGS:', totalUrls);
console.log('  - Share links (/share/): ' + allShareIds.length);
console.log('  - Numeric IDs (/item/, edit, HTML): ' + allNumericIds.size);

if (totalUrls > 0) {
  console.log('✅ Would extract', totalUrls, 'items');
  if (allShareIds.length > 0) {
    console.log('Sample share URLs:', allShareIds.slice(0, 3).map(id => 'https://www.facebook.com/share/' + id));
  } else if (allNumericIds.size > 0) {
    console.log('Sample item URLs:', Array.from(allNumericIds).slice(0, 3).map(id => 'https://www.facebook.com/marketplace/item/' + id));
  }
} else {
  console.log('❌ NO LISTINGS FOUND');
  console.log('Try:');
  console.log('1. Scroll down to load listings');
  console.log('2. Make sure you have published (not draft) listings');
  console.log('3. Refresh page');
  console.log('4. Check if Facebook changed the page structure again');
}
console.log('='.repeat(80));
