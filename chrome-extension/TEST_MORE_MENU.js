/**
 * DEBUG SCRIPT - Test "..." More menu for share links
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 *
 * This script:
 * 1. Finds the "More options" (...) menu buttons
 * 2. Clicks them to open the dropdown
 * 3. Looks for "Share" or "Copy link" options
 * 4. Extracts the share URL if found
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Test More Menu Approach');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

// Helper function to wait
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Helper to close any open menus
function closeMenus() {
  document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', keyCode: 27 }));
  document.body.click();
}

// Find "More options" menu buttons
console.log('🔍 Finding "More options" menu buttons...');
const menuButtons = Array.from(document.querySelectorAll('[aria-label*="More options"]'));

console.log(`✅ Found ${menuButtons.length} menu buttons\n`);

if (menuButtons.length === 0) {
  console.log('❌ No menu buttons found. Scroll down and run again.');
} else {
  // Test first 2 listings
  const maxToTest = Math.min(menuButtons.length, 2);
  console.log(`🧪 Testing first ${maxToTest} menu(s)...\n`);

  async function testMenus() {
    for (let i = 0; i < maxToTest; i++) {
      const menuButton = menuButtons[i];
      const ariaLabel = menuButton.getAttribute('aria-label');

      console.log('='.repeat(80));
      console.log(`📦 MENU ${i + 1}/${maxToTest}`);
      console.log('='.repeat(80));
      console.log(`Aria-label: ${ariaLabel}`);

      // Extract title from aria-label
      const titleMatch = ariaLabel.match(/More options for (.+)/);
      const title = titleMatch ? titleMatch[1] : 'Unknown';
      console.log(`Title: "${title}"`);

      try {
        // Close any open menus
        closeMenus();
        await wait(300);

        // Scroll menu button into view
        menuButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await wait(500);

        console.log('\n🖱️  Clicking "More options" button...');
        menuButton.click();
        await wait(1500); // Wait for dropdown to appear

        console.log('🔍 Looking for menu items...');

        // Find the dropdown menu
        const dropdown = document.querySelector('[role="menu"]');

        if (!dropdown) {
          console.log('  ⚠️  No menu appeared');
          continue;
        }

        console.log('  ✅ Dropdown menu appeared');

        // Find all menu items
        const menuItems = dropdown.querySelectorAll('[role="menuitem"], a, div[role="button"], span');
        console.log(`  Found ${menuItems.length} potential menu items`);

        console.log('\n  📋 Menu items:');

        let foundShareLink = null;

        for (const item of menuItems) {
          const text = item.textContent.trim();
          const href = item.getAttribute('href') || '';
          const ariaLabel = item.getAttribute('aria-label') || '';

          if (text.length > 0 && text.length < 100) {
            console.log(`    - "${text}"`);

            // Check for share-related items
            if (text.includes('Share') || text.includes('Copy link') || text.includes('share')) {
              console.log(`      ⭐ SHARE-RELATED ITEM!`);

              // Check if it has a href
              if (href) {
                console.log(`      Href: ${href}`);
                if (href.includes('/share/')) {
                  foundShareLink = href;
                  console.log(`      🎉🎉🎉 FOUND SHARE URL!`);
                }
              }

              // Check for nested links
              const nestedLink = item.querySelector('a[href]');
              if (nestedLink) {
                const nestedHref = nestedLink.getAttribute('href');
                console.log(`      Nested href: ${nestedHref}`);
                if (nestedHref.includes('/share/')) {
                  foundShareLink = nestedHref;
                  console.log(`      🎉🎉🎉 FOUND SHARE URL (nested)!`);
                }
              }

              // Check if clicking this item might trigger clipboard
              console.log(`      Trying to click this item...`);
              item.click();
              await wait(500);

              // Try reading clipboard
              try {
                const clipboardText = await navigator.clipboard.readText();
                console.log(`      Clipboard: ${clipboardText.substring(0, 100)}`);

                if (clipboardText.includes('/share/') || clipboardText.includes('facebook.com')) {
                  foundShareLink = clipboardText;
                  console.log(`      🎉🎉🎉 FOUND SHARE URL IN CLIPBOARD!`);
                }
              } catch (clipErr) {
                console.log(`      ⚠️  Cannot read clipboard: ${clipErr.message}`);
              }

              // Close any dialogs that might have opened
              closeMenus();
              await wait(300);
            }
          }
        }

        // Also search dropdown HTML for share URLs
        console.log('\n  🔎 Searching dropdown HTML...');
        const dropdownHtml = dropdown.outerHTML;

        const shareMatches = dropdownHtml.match(/\/share\/[a-zA-Z0-9]{8,20}/g);
        if (shareMatches && shareMatches.length > 0) {
          console.log(`    ⭐⭐⭐ Found share URLs in HTML: ${shareMatches.length}`);
          [...new Set(shareMatches)].forEach((match, idx) => {
            console.log(`      ${idx + 1}. ${match}`);
          });
          if (!foundShareLink) {
            foundShareLink = shareMatches[0];
          }
        }

        // Final result for this listing
        console.log('\n  📊 RESULT:');
        if (foundShareLink) {
          // Clean up URL
          if (foundShareLink.startsWith('/')) {
            foundShareLink = 'https://www.facebook.com' + foundShareLink;
          }
          foundShareLink = foundShareLink.split('?')[0];

          console.log(`    ✅ SUCCESS!`);
          console.log(`    Share URL: ${foundShareLink}`);
          console.log(`    Title: "${title}"`);
        } else {
          console.log(`    ❌ No share URL found`);
        }

        // Close the dropdown
        closeMenus();
        await wait(500);

      } catch (err) {
        console.error(`  ❌ Error: ${err.message}`);
        closeMenus();
      }

      console.log('\n');
    }

    // Final summary
    console.log('='.repeat(80));
    console.log('💡 CONCLUSION');
    console.log('='.repeat(80));
    console.log('If share URLs were found in the "More options" menu,');
    console.log('we can update the extension to use this approach.');
    console.log('');
    console.log('If NOT found, we need to try a different approach:');
    console.log('1. Click listing image/title area (even without link)');
    console.log('2. Listen for navigation or modal opening');
    console.log('3. Extract share URL from resulting page');
    console.log('='.repeat(80));
  }

  // Run the test
  console.log('⏳ Starting test in 2 seconds...\n');
  setTimeout(() => {
    testMenus().then(() => {
      console.log('\n✅ Test complete!');
    });
  }, 2000);
}
