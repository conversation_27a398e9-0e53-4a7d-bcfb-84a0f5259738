/**
 * DEBUG SCRIPT - Test clicking listing to find share URL
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 *
 * This script tests:
 * 1. Clicking a listing image/title
 * 2. Seeing where it navigates
 * 3. Finding share URL on the resulting page
 * 4. Extracting product data from that page
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Test Listing Click Navigation');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

// Find Share buttons to locate listing cards
console.log('🔍 Finding listing cards via Share buttons...');
let shareButtons = Array.from(document.querySelectorAll('[aria-label="Share"]'));

if (shareButtons.length === 0) {
  const allClickable = document.querySelectorAll('div[role="button"], span, a, button');
  shareButtons = Array.from(allClickable).filter(el => {
    const text = el.textContent.trim();
    return text === 'Share' || text === 'share';
  });
}

console.log(`Found ${shareButtons.length} Share buttons (listing cards)\n`);

if (shareButtons.length === 0) {
  console.log('❌ No listings found. Scroll down and run again.');
} else {
  // Analyze first listing
  console.log('📦 Analyzing FIRST listing card...\n');

  const shareButton = shareButtons[0];

  // Find the container
  let container = shareButton;
  for (let i = 0; i < 10; i++) {
    const hasImage = container.querySelector('img') !== null;
    const hasPrice = container.textContent.match(/\$\d+/);
    if (hasImage && hasPrice) {
      break;
    }
    container = container.parentElement;
  }

  console.log('Container found:', container.tagName);

  // Find all clickable elements in the container
  console.log('\n🔗 Clickable elements in listing card:');

  const links = container.querySelectorAll('a[href]');
  console.log(`  Found ${links.length} links`);

  links.forEach((link, idx) => {
    const href = link.getAttribute('href');
    const text = link.textContent.trim().substring(0, 50);
    console.log(`  ${idx + 1}. ${href.substring(0, 80)}`);
    console.log(`     Text: "${text}"`);
    console.log(`     Role: ${link.getAttribute('role') || '(none)'}`);
  });

  // Find the main listing link (usually the title or image)
  console.log('\n🎯 Looking for main listing link...');

  // Try to find title link
  const titleLinks = Array.from(links).filter(link => {
    const text = link.textContent.trim();
    return text.length > 10 && !text.includes('Mark') && !text.includes('Share');
  });

  console.log(`  Found ${titleLinks.length} potential title links`);

  if (titleLinks.length > 0) {
    const titleLink = titleLinks[0];
    const href = titleLink.getAttribute('href');
    const text = titleLink.textContent.trim();

    console.log('\n✅ FOUND MAIN LISTING LINK:');
    console.log(`  Text: "${text.substring(0, 60)}"`);
    console.log(`  URL: ${href}`);

    // Analyze the URL
    console.log('\n🔍 URL Analysis:');

    if (href.includes('/share/')) {
      const shareId = href.match(/\/share\/([a-zA-Z0-9]+)/);
      console.log('  ⭐⭐⭐ IT\'S A SHARE URL!');
      console.log(`  Share ID: ${shareId ? shareId[1] : 'unknown'}`);
      console.log(`  Full URL: https://www.facebook.com${href.startsWith('/') ? href : '/' + href}`);
      console.log('\n✅ SUCCESS! We can extract share URLs directly from listing card links!');
    } else if (href.includes('/item/')) {
      const itemId = href.match(/\/item\/(\d+)/);
      console.log('  ⭐ IT\'S AN ITEM URL!');
      console.log(`  Item ID: ${itemId ? itemId[1] : 'unknown'}`);
      console.log(`  Full URL: https://www.facebook.com${href.startsWith('/') ? href : '/' + href}`);
      console.log('\n✅ We can extract item URLs and navigate to them!');
    } else if (href.includes('listing_id=')) {
      const listingId = href.match(/listing_id=(\d+)/);
      console.log('  ⭐ IT HAS A LISTING_ID PARAMETER!');
      console.log(`  Listing ID: ${listingId ? listingId[1] : 'unknown'}`);
      console.log(`  Full URL: https://www.facebook.com${href.startsWith('/') ? href : '/' + href}`);
      console.log('\n✅ We can extract listing IDs and construct share URLs!');
    } else if (href.startsWith('/')) {
      console.log('  ⚠️  Relative URL - need to investigate where it goes');
      console.log(`  Full URL: https://www.facebook.com${href}`);
    } else if (href.startsWith('http')) {
      console.log('  ⚠️  External URL');
    } else {
      console.log('  ⚠️  Unknown URL format');
    }

    // Test navigation
    console.log('\n' + '='.repeat(80));
    console.log('🧪 NAVIGATION TEST');
    console.log('='.repeat(80));
    console.log('\nWe will now:');
    console.log('1. Click the listing link (or navigate to it)');
    console.log('2. Wait for page to load');
    console.log('3. Check the resulting URL');
    console.log('4. Look for share URL on that page');
    console.log('\n⚠️  This will navigate away from the selling page!');
    console.log('⚠️  You can press ESC or go Back to return to selling page');
    console.log('\nStarting navigation test in 3 seconds...\n');

    setTimeout(() => {
      console.log('🚀 Navigating to listing page...');

      // Save current URL
      const sellingPageUrl = window.location.href;

      // Navigate to listing
      const fullUrl = href.startsWith('http') ? href : `https://www.facebook.com${href}`;
      window.location.href = fullUrl;

      // After navigation (this won't run in this context, but shows the intent)
      // We'll need to detect page load and then run analysis
      setTimeout(() => {
        console.log('\n📍 Current URL:', window.location.href);

        // Look for share URL on the page
        console.log('🔍 Looking for share URL on this page...');

        const shareLinks = document.querySelectorAll('a[href*="/share/"]');
        console.log(`  Found ${shareLinks.length} share links`);

        if (shareLinks.length > 0) {
          shareLinks.forEach((link, idx) => {
            const shareHref = link.getAttribute('href');
            console.log(`  ${idx + 1}. ${shareHref}`);
          });

          console.log('\n✅ SUCCESS! Share URL is visible on listing page!');
        } else {
          console.log('  ❌ No share links found on this page');

          // Try HTML source search
          const html = document.documentElement.outerHTML;
          const shareMatches = html.match(/\/share\/[a-zA-Z0-9]{8,20}/g);

          if (shareMatches && shareMatches.length > 0) {
            console.log('\n  🔎 Found share URLs in HTML source:');
            [...new Set(shareMatches)].forEach((match, idx) => {
              console.log(`    ${idx + 1}. ${match}`);
            });
            console.log('\n✅ Share URLs are hidden in HTML!');
          }
        }

        console.log('\n' + '='.repeat(80));
        console.log('💡 INSTRUCTIONS');
        console.log('='.repeat(80));
        console.log('To return to selling page:');
        console.log(`  window.location.href = "${sellingPageUrl}"`);
        console.log('Or just click the Back button');
        console.log('='.repeat(80));

      }, 3000); // Wait 3 seconds after navigation

    }, 3000);

  } else {
    console.log('\n❌ No title link found in listing card');
    console.log('\nTrying image link instead...');

    const images = container.querySelectorAll('img');
    if (images.length > 0) {
      const img = images[0];
      const imgParent = img.parentElement;

      if (imgParent.tagName === 'A') {
        const href = imgParent.getAttribute('href');
        console.log(`\n✅ Found image link: ${href}`);
        console.log('This might be the main listing link');
      } else {
        console.log('\n❌ Image is not wrapped in a link');
      }
    }
  }

  console.log('\n' + '='.repeat(80));
}
