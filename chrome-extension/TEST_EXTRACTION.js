/**
 * TEST EXTRACTION - Paste in Console on Facebook Marketplace Selling Page
 *
 * This will test if the new fetch-based extraction is working
 */

(async function testExtraction() {
  console.log('🧪 TESTING EXTRACTION LOGIC');
  console.log('='.repeat(80));

  // Step 1: Find item URLs
  const itemUrls = new Set();
  const allLinks = document.querySelectorAll('a');

  console.log('Total links on page:', allLinks.length);

  allLinks.forEach(link => {
    const href = link.getAttribute('href');
    if (href && href.includes('/item/')) {
      let fullUrl = href;
      if (href.startsWith('/')) {
        fullUrl = 'https://www.facebook.com' + href;
      }
      fullUrl = fullUrl.split('?')[0];
      itemUrls.add(fullUrl);
    }
  });

  console.log('📋 Found ' + itemUrls.size + ' item URLs');

  if (itemUrls.size === 0) {
    console.error('❌ NO ITEMS FOUND!');
    console.error('Current URL:', window.location.href);
    console.error('Make sure you are on: https://www.facebook.com/marketplace/you/selling');
    return;
  }

  // Test with first URL
  const testUrl = Array.from(itemUrls)[0];
  console.log('\n🎯 Testing with first URL:', testUrl);
  console.log('='.repeat(80));

  try {
    // Fetch HTML
    console.log('📥 Fetching HTML...');
    const response = await fetch(testUrl);
    const html = await response.text();
    console.log('✅ Fetched ' + (html.length / 1024).toFixed(1) + ' KB');

    // Parse HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    console.log('✅ Parsed document');

    // Test extraction
    console.log('\n🔍 TESTING DATA EXTRACTION:');
    console.log('-'.repeat(80));

    // 1. Title extraction
    console.log('\n1️⃣ TITLE EXTRACTION:');
    const h1 = doc.querySelector('h1');
    console.log('   H1 tag:', h1 ? h1.textContent.trim() : 'NOT FOUND');

    const spanAutoDir = doc.querySelectorAll('span[dir="auto"]');
    console.log('   Found ' + spanAutoDir.length + ' span[dir="auto"] elements');
    if (spanAutoDir.length > 0) {
      console.log('   First 5:');
      Array.from(spanAutoDir).slice(0, 5).forEach((el, i) => {
        const text = el.textContent.trim();
        if (text.length > 3 && text.length < 200) {
          console.log('     ' + (i + 1) + '. ' + text.substring(0, 80));
        }
      });
    }

    // 2. Price extraction
    console.log('\n2️⃣ PRICE EXTRACTION:');
    const bodyText = doc.body.textContent;
    const priceMatches = bodyText.match(/\$([0-9,]+(?:\.\d{2})?)/g);
    console.log('   Price patterns found:', priceMatches ? priceMatches.slice(0, 5) : 'NONE');

    // 3. Description extraction
    console.log('\n3️⃣ DESCRIPTION EXTRACTION:');
    const textElements = doc.querySelectorAll('span[dir="auto"], div[dir="auto"]');
    console.log('   Found ' + textElements.length + ' text elements');
    const longTexts = [];
    for (const el of textElements) {
      const text = el.textContent.trim();
      if (text.length > 30 && text.length < 5000) {
        longTexts.push(text);
      }
    }
    console.log('   Long text blocks (' + longTexts.length + '):');
    longTexts.slice(0, 3).forEach((text, i) => {
      console.log('     ' + (i + 1) + '. (' + text.length + ' chars): ' + text.substring(0, 100) + '...');
    });

    // 4. Image extraction
    console.log('\n4️⃣ IMAGE EXTRACTION:');
    const allImages = doc.querySelectorAll('img');
    console.log('   Total img tags:', allImages.length);

    const scontentImages = [];
    for (const img of allImages) {
      const src = img.src || img.getAttribute('data-src') || img.getAttribute('src');
      if (src && src.includes('scontent')) {
        scontentImages.push(src);
      }
    }
    console.log('   Scontent images:', scontentImages.length);
    if (scontentImages.length > 0) {
      console.log('   Sample:');
      scontentImages.slice(0, 3).forEach((src, i) => {
        console.log('     ' + (i + 1) + '. ' + src.substring(0, 120) + '...');
      });
    } else {
      console.error('   ❌ NO SCONTENT IMAGES FOUND!');
      console.log('   Sample of all image sources:');
      Array.from(allImages).slice(0, 5).forEach((img, i) => {
        const src = img.src || img.getAttribute('data-src') || img.getAttribute('src') || '(no src)';
        console.log('     ' + (i + 1) + '. ' + src.substring(0, 120));
      });
    }

    // 5. Video extraction
    console.log('\n5️⃣ VIDEO EXTRACTION:');
    const videos = doc.querySelectorAll('video, source[type*="video"]');
    console.log('   Videos found:', videos.length);

    console.log('\n='.repeat(80));
    console.log('✅ TEST COMPLETE');
    console.log('='.repeat(80));

    // Summary
    console.log('\n📊 SUMMARY:');
    console.log('   URLs found: ' + itemUrls.size);
    console.log('   HTML fetched: ' + (html.length / 1024).toFixed(1) + ' KB');
    console.log('   Images found: ' + scontentImages.length);
    console.log('   Long text blocks: ' + longTexts.length);

    if (scontentImages.length === 0) {
      console.error('\n⚠️  WARNING: NO IMAGES FOUND!');
      console.error('   This means the fetch() method is not getting the full page.');
      console.error('   Facebook likely loads images dynamically with JavaScript.');
      console.error('\n💡 SOLUTION: Need to use a different extraction method.');
      console.error('   Option 1: Navigate to each page and extract from rendered DOM');
      console.error('   Option 2: Use Facebook Graph API (requires permissions)');
      console.error('   Option 3: Extract from the selling page thumbnails');
    }

  } catch (error) {
    console.error('❌ TEST FAILED:', error);
    console.error('Stack:', error.stack);
  }
})();
