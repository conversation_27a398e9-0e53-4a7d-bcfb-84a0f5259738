# Facebook Extension Image Extraction Fix - Testing Instructions

## Problem Fixed

The extension was extracting 209 listings but ALL had 0 images because Facebook uses lazy loading. The listing cards (`div[role="button"]`) exist in the DOM immediately, but images and content only load when scrolled into view.

## What Changed

### File: `scripts/content.js`

**Function**: `waitForListingsToLoad()` (lines 373-412)

**Key Changes**:
1. Added check for div buttons that have BOTH prices AND images loaded
2. Wait condition now checks if at least 5 div buttons have actual content (not empty shells)
3. Enhanced logging to show "div buttons with images: X"

**New Code**:
```javascript
// NEW: Check for div buttons with prices and images (new structure)
const divButtons = Array.from(document.querySelectorAll('div[role="button"]')).filter(div => {
  const text = div.textContent || '';
  const hasPrice = text.includes('$') && text.length > 20;
  const hasImage = div.querySelector('img[src*="scontent"]') !== null;  // ✅ NEW CHECK
  return hasPrice && hasImage;
});

console.log('Waiting... (links: ' + totalLinks + ', item links: ' + itemLinksCount + ', div buttons with images: ' + divButtons.length + ')');

// If we found any item links OR div buttons with images
if (itemLinksCount > 0 || divButtons.length > 5) {  // ✅ WAIT FOR 5+ DIV BUTTONS WITH CONTENT
  console.log('Page loaded! Found ' + itemLinksCount + ' item links and ' + divButtons.length + ' div buttons with content');
  await new Promise(resolve => setTimeout(resolve, 1000)); // Extra 1s to be safe
  return;
}
```

## How to Test

### Step 1: Reload Extension
1. Open Chrome and go to `chrome://extensions`
2. Find "OneOf Store Facebook Importer"
3. Click the reload icon (circular arrow)

### Step 2: Navigate to Facebook Marketplace
1. Go to https://facebook.com/marketplace
2. Click "You" in the left sidebar
3. Click "Selling" to see all your active listings
4. **IMPORTANT**: Scroll down the page manually for 10-15 seconds to trigger Facebook's lazy loading
   - This will pre-load the images
   - Watch the page load more content as you scroll

### Step 3: Run the Extension
1. Scroll back to the top of the page
2. Click the extension icon in Chrome toolbar
3. Make sure API URL is set (e.g., `http://localhost:8025/api`)
4. Check "Skip auto-scroll" option (since you already scrolled manually)
5. Click "Import ALL New Listings"

### Step 4: Check Console Logs
Open DevTools Console (F12 → Console tab) and look for:

**BEFORE (broken)**:
```
Waiting... (links: 21, item links: 0)
Found 209 div[role="button"] with $ (potential listings)
⚠️ NO IMAGE Extracted: Item Title - $50 (0 images)
⚠️ NO IMAGE Extracted: Another Item - $100 (0 images)
```

**AFTER (fixed)**:
```
Waiting... (links: 21, item links: 0, div buttons with images: 25)
Page loaded! Found 0 item links and 25 div buttons with content
✅ Extracted: Item Title - $50 (3 images)
✅ Extracted: Another Item - $100 (5 images)
```

### Expected Results
- Console should show "div buttons with images: X" where X > 5
- Each extraction should show number of images: "(3 images)", "(5 images)", etc.
- NO "⚠️ NO IMAGE" warnings
- Imported items in store should have cover images

## Alternative Approach (If Still No Images)

If the fix doesn't work, the issue may be that Facebook's lazy loading hasn't triggered yet even after manual scrolling. In that case, we need a more aggressive solution:

### Option A: Force Lazy Loading
Update the extension to:
1. Programmatically scroll the page slowly to trigger IntersectionObserver
2. Wait for images to appear in viewport
3. Then extract

### Option B: Extract Item URLs First
Instead of extracting from div buttons, find the clickable links that wrap the cards and extract full details from individual listing pages.

## Status

✅ Fix implemented in `content.js`
⏳ Awaiting test results
📝 Next step: Test and verify images now extract properly

## If It Works

If images now extract successfully:
1. The extension is working correctly
2. You can import all your Facebook Marketplace listings with images
3. No further changes needed

## If It Doesn't Work

If you still see 0 images:
1. Share the new console logs
2. I'll implement the more aggressive lazy-loading solution
3. May need to add a "Pre-load" step that scrolls the page automatically before extraction
