/**
 * DEBUG SCRIPT - Comprehensive page analysis
 * Run this in the browser console on your Facebook Marketplace selling page
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - COMPREHENSIVE PAGE ANALYSIS');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

// 1. Basic link analysis
const allLinks = document.querySelectorAll('a');
console.log('📊 LINK ANALYSIS');
console.log('Total links:', allLinks.length);
console.log('');

// Show sample of ALL links
console.log('Sample of first 20 links on page:');
Array.from(allLinks).slice(0, 20).forEach((link, idx) => {
  const href = link.getAttribute('href') || '(no href)';
  const text = link.textContent.trim().substring(0, 50) || '(no text)';
  console.log(`${idx + 1}. ${href.substring(0, 80)} | Text: ${text}`);
});
console.log('');

// 2. Check for different URL patterns
console.log('🔍 URL PATTERN ANALYSIS');

const patterns = {
  '/share/': 0,
  '/item/': 0,
  'listing_id=': 0,
  '/marketplace/': 0,
  '/selling': 0,
  '/profile': 0,
  'javascript:': 0,
  'http': 0
};

allLinks.forEach(link => {
  const href = link.getAttribute('href') || '';
  for (const pattern in patterns) {
    if (href.includes(pattern)) {
      patterns[pattern]++;
    }
  }
});

for (const pattern in patterns) {
  console.log(`  ${pattern}: ${patterns[pattern]}`);
}
console.log('');

// 3. Check for images
console.log('🖼️  IMAGE ANALYSIS');
const allImages = document.querySelectorAll('img');
console.log('Total images:', allImages.length);

const imageTypes = {
  'scontent (product images)': 0,
  'profile images': 0,
  'static images': 0,
  'other': 0
};

allImages.forEach(img => {
  const src = img.src || img.getAttribute('data-src') || '';
  if (src.includes('scontent') && !src.includes('profile')) {
    imageTypes['scontent (product images)']++;
  } else if (src.includes('profile')) {
    imageTypes['profile images']++;
  } else if (src.includes('static')) {
    imageTypes['static images']++;
  } else {
    imageTypes['other']++;
  }
});

for (const type in imageTypes) {
  console.log(`  ${type}: ${imageTypes[type]}`);
}
console.log('');

// 4. Check for listing cards or containers
console.log('📦 LISTING CONTAINER ANALYSIS');

const possibleSelectors = [
  'div[role="article"]',
  'div[data-testid*="marketplace"]',
  'div[aria-label*="listing"]',
  'a[href*="/item/"]',
  'a[href*="/share/"]',
  'div.x1qjc9v5', // Common FB class for cards
  '[data-store*="listing"]'
];

possibleSelectors.forEach(selector => {
  try {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
      console.log(`  ✅ ${selector}: ${elements.length} found`);
    }
  } catch (e) {
    // Invalid selector
  }
});
console.log('');

// 5. Search HTML source for any recognizable IDs
console.log('🔎 HTML SOURCE ANALYSIS');
const html = document.documentElement.outerHTML;

const patterns2 = [
  /listing[_\-]?id[=:"\\](\d{15,20})/gi,
  /marketplace[\/]item[\/](\d{15,20})/gi,
  /share[\/]([a-zA-Z0-9]{8,20})/gi,
  /"id":"(\d{15,20})"/gi
];

patterns2.forEach((pattern, idx) => {
  const matches = html.match(pattern);
  if (matches && matches.length > 0) {
    console.log(`  Pattern ${idx + 1}: Found ${matches.length} matches`);
    console.log('    Samples:', matches.slice(0, 3));
  } else {
    console.log(`  Pattern ${idx + 1}: 0 matches`);
  }
});
console.log('');

// 6. Check page text for indicators
console.log('📝 PAGE TEXT ANALYSIS');
const bodyText = document.body.textContent || '';
const indicators = {
  'Selling': bodyText.includes('Selling'),
  'Your listings': bodyText.includes('Your listings'),
  'No listings': bodyText.includes('No listings') || bodyText.includes("You don't have any"),
  'Draft': bodyText.includes('Draft'),
  'Active': bodyText.includes('Active'),
  'Sold': bodyText.includes('Sold')
};

for (const indicator in indicators) {
  console.log(`  ${indicator}: ${indicators[indicator] ? '✅' : '❌'}`);
}
console.log('');

// 7. Try to find listing elements by other means
console.log('🎯 TRYING TO FIND ACTUAL LISTINGS');

// Look for price indicators
const priceElements = document.querySelectorAll('*');
const pricesFound = [];
priceElements.forEach(el => {
  const text = el.textContent || '';
  const priceMatch = text.match(/^\$\d+/);
  if (priceMatch && el.textContent.length < 20) {
    pricesFound.push(el.textContent.trim());
  }
});

if (pricesFound.length > 0) {
  console.log('  💰 Found price indicators:', pricesFound.slice(0, 5));
} else {
  console.log('  💰 No price indicators found');
}
console.log('');

// 8. Final recommendations
console.log('='.repeat(80));
console.log('💡 RECOMMENDATIONS');
console.log('='.repeat(80));

if (allLinks.length < 50) {
  console.log('⚠️  Very few links found - page may not be loaded');
  console.log('   Try: Scroll down, wait a few seconds, then run script again');
}

if (allImages.length < 10) {
  console.log('⚠️  Very few images found - listings may not be visible');
  console.log('   Try: Scroll to see your listings, then run script again');
}

if (!indicators['Selling'] && !indicators['Your listings']) {
  console.log('❌ Page doesn\'t seem to be the selling page');
  console.log('   Expected URL: https://www.facebook.com/marketplace/you/selling');
  console.log('   Actual URL: ' + window.location.href);
}

if (indicators['No listings']) {
  console.log('❌ Page says "No listings" - do you have published items?');
}

console.log('');
console.log('📋 NEXT STEPS:');
console.log('1. Scroll down to load listings');
console.log('2. Wait for images to appear');
console.log('3. Run this script again');
console.log('4. If still no results, manually click a listing and copy its URL');
console.log('='.repeat(80));
