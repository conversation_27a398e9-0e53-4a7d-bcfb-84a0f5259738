/**
 * TEST NEW EXTRACTION - Test the updated extraction logic
 * Paste this on the selling page
 */

(function testNewExtraction() {
  console.log('🧪 TESTING NEW EXTRACTION LOGIC');
  console.log('='.repeat(80));

  const itemUrls = new Set();
  const allLinks = document.querySelectorAll('a');

  console.log('Total links:', allLinks.length);

  // Method 1: Edit URLs
  console.log('\n1️⃣ METHOD 1: Looking for edit URLs...');
  let method1Count = 0;
  allLinks.forEach(link => {
    const href = link.getAttribute('href');
    if (href && href.includes('listing_id=')) {
      const match = href.match(/listing_id=(\d+)/);
      if (match) {
        const listingId = match[1];
        const itemUrl = 'https://www.facebook.com/marketplace/item/' + listingId;
        itemUrls.add(itemUrl);
        console.log('   Found: ' + listingId + ' → ' + itemUrl);
        method1Count++;
      }
    }
  });
  console.log('   Total from method 1: ' + method1Count);

  // Method 2: Search HTML
  if (itemUrls.size === 0) {
    console.log('\n2️⃣ METHOD 2: Searching page HTML...');
    const html = document.documentElement.outerHTML;
    const listingIdMatches = html.match(/listing[_\-\/]id[=:](\d{15,20})/gi);

    if (listingIdMatches) {
      const uniqueMatches = [...new Set(listingIdMatches)];
      console.log('   Found ' + uniqueMatches.length + ' patterns:');
      uniqueMatches.slice(0, 10).forEach(match => {
        console.log('     ' + match);
        const idMatch = match.match(/(\d{15,20})/);
        if (idMatch) {
          const listingId = idMatch[1];
          const itemUrl = 'https://www.facebook.com/marketplace/item/' + listingId;
          itemUrls.add(itemUrl);
        }
      });
    }
    console.log('   Total from method 2: ' + itemUrls.size);
  }

  // Method 3: Old /item/ links
  if (itemUrls.size === 0) {
    console.log('\n3️⃣ METHOD 3: Old /item/ link method...');
    let method3Count = 0;
    allLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href && href.includes('/item/')) {
        let fullUrl = href;
        if (href.startsWith('/')) {
          fullUrl = 'https://www.facebook.com/marketplace/item' + href;
        }
        fullUrl = fullUrl.split('?')[0];
        itemUrls.add(fullUrl);
        method3Count++;
      }
    });
    console.log('   Total from method 3: ' + method3Count);
  }

  console.log('\n='.repeat(80));
  console.log('📊 RESULTS:');
  console.log('   Total item URLs found: ' + itemUrls.size);
  console.log('');

  if (itemUrls.size > 0) {
    console.log('✅ SUCCESS! Found URLs:');
    Array.from(itemUrls).forEach((url, i) => {
      console.log('   ' + (i + 1) + '. ' + url);
    });
    console.log('');
    console.log('💡 The extension should work now!');
    console.log('   Click the extension icon to import these listings.');
  } else {
    console.error('❌ NO URLS FOUND');
    console.error('');
    console.error('This means you likely have:');
    console.error('1. No published active listings (only drafts)');
    console.error('2. Or Facebook changed the structure again');
    console.error('');
    console.error('If you see a "Continue" button for a draft:');
    console.error('  → Click it and publish the listing first');
    console.error('  → Then refresh and try again');
  }

  console.log('='.repeat(80));
})();
