/**
 * DEBUG SCRIPT - Find where share URLs are hidden in the page
 * Run this in the browser console on https://www.facebook.com/marketplace/you/selling
 */

console.clear();
console.log('='.repeat(80));
console.log('OneOf Store Extension - Find Share URLs in Page');
console.log('='.repeat(80));
console.log('Current URL:', window.location.href);
console.log('');

// Known share URLs from user
const knownShareUrls = [
  '1A4fDc8h9d',
  '17TmY97R2p',
  '15d4t7VWDr',
  '1BZAyrvRq9'
];

console.log('🔍 Looking for these share IDs in the page:');
knownShareUrls.forEach((id, idx) => {
  console.log(`  ${idx + 1}. ${id}`);
});
console.log('');

// Search the entire page HTML
const html = document.documentElement.outerHTML;
console.log(`📄 Page HTML length: ${html.length.toLocaleString()} characters`);
console.log('');

// Search for each known share URL
console.log('='.repeat(80));
console.log('🔎 SEARCHING PAGE HTML FOR SHARE URLs');
console.log('='.repeat(80));
console.log('');

knownShareUrls.forEach((shareId, idx) => {
  console.log(`${idx + 1}. Searching for: ${shareId}`);

  // Check if it exists in HTML
  if (html.includes(shareId)) {
    console.log(`   ✅ FOUND in HTML!`);

    // Find all occurrences
    const regex = new RegExp(shareId, 'g');
    const matches = html.match(regex);
    console.log(`   Occurrences: ${matches.length}`);

    // Find the surrounding context (100 chars before and after)
    const index = html.indexOf(shareId);
    const start = Math.max(0, index - 100);
    const end = Math.min(html.length, index + shareId.length + 100);
    const context = html.substring(start, end);

    console.log(`   Context:`);
    console.log(`   ${context.substring(0, 200)}`);
    console.log('');
  } else {
    console.log(`   ❌ NOT found in HTML`);
    console.log('');
  }
});

// Now search for ALL share URLs in the page
console.log('='.repeat(80));
console.log('🔍 SEARCHING FOR ALL SHARE URLs ON PAGE');
console.log('='.repeat(80));
console.log('');

// Pattern to match /share/ URLs
const shareUrlPattern = /\/share\/([a-zA-Z0-9]{8,20})/g;
const allShareMatches = html.match(shareUrlPattern);

if (allShareMatches) {
  const uniqueUrls = [...new Set(allShareMatches)];
  console.log(`✅ Found ${uniqueUrls.length} unique share URLs in page HTML:`);
  console.log('');

  uniqueUrls.slice(0, 10).forEach((url, idx) => {
    console.log(`  ${idx + 1}. ${url}`);
  });

  if (uniqueUrls.length > 10) {
    console.log(`  ... and ${uniqueUrls.length - 10} more`);
  }

  console.log('');
  console.log('🎉 SUCCESS! Share URLs ARE in the page HTML!');
  console.log('');

  // Find where the first one is located
  const firstShareUrl = uniqueUrls[0];
  const firstIndex = html.indexOf(firstShareUrl);
  const contextStart = Math.max(0, firstIndex - 200);
  const contextEnd = Math.min(html.length, firstIndex + 200);
  const firstContext = html.substring(contextStart, contextEnd);

  console.log('📍 Location of first share URL:');
  console.log(firstContext);
  console.log('');

} else {
  console.log('❌ No share URLs found in page HTML');
  console.log('');
}

// Try to find the URLs as clickable links
console.log('='.repeat(80));
console.log('🔗 SEARCHING FOR SHARE URLs AS CLICKABLE LINKS');
console.log('='.repeat(80));
console.log('');

const allLinks = document.querySelectorAll('a[href]');
console.log(`Total <a> links on page: ${allLinks.length}`);

const shareLinks = Array.from(allLinks).filter(link => {
  const href = link.getAttribute('href');
  return href && href.includes('/share/');
});

console.log(`Share links (<a href="/share/...">): ${shareLinks.length}`);

if (shareLinks.length > 0) {
  console.log('');
  shareLinks.slice(0, 10).forEach((link, idx) => {
    const href = link.getAttribute('href');
    const text = link.textContent.trim().substring(0, 40);
    console.log(`  ${idx + 1}. ${href}`);
    console.log(`     Text: "${text}"`);
  });

  if (shareLinks.length > 10) {
    console.log(`  ... and ${shareLinks.length - 10} more`);
  }
} else {
  console.log('❌ No <a> elements with share URLs found');
}

console.log('');
console.log('='.repeat(80));
console.log('💡 NEXT STEP');
console.log('='.repeat(80));
console.log('');
console.log('If share URLs were found in HTML:');
console.log('  → They might be in JSON data embedded in the page');
console.log('  → Or in data attributes');
console.log('  → Or in script tags');
console.log('');
console.log('Run EXTRACT_FROM_PAGE_DATA.js next to extract them');
console.log('='.repeat(80));
