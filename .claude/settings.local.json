{"permissions": {"allow": ["Bash(ssh:*)", "<PERSON><PERSON>(curl:*)", "Bash(rsync:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(python3:*)", "Read(//home/<USER>/Documents/**)", "<PERSON><PERSON>(tee:*)", "Bash(TOKEN=\"changeme-super-secret-admin-api-bearer-moskva17\")", "Bash(__NEW_LINE__ echo \"Deleting all category placeholder items...\")", "Bash(for id in 260 259 258 257 256 255 254 253 252 251)", "Bash(do)", "<PERSON><PERSON>(echo:*)", "Bash(done)", "Bash(__NEW_LINE__ echo -e \"\\n✅ All placeholder items deleted!\")", "Read(//home/<USER>/Downloads/looongshots/**)", "Bash(__NEW_LINE__ echo \"Deleting 17 placeholder items...\")", "Bash(for id in 41 42 39 40 37 38 36 34 35 32 33 30 31 29 27 28 25)", "Bash(__NEW_LINE__ echo -e \"\\n✅ All 17 placeholder items deleted!\")", "Bash(node -c:*)"], "deny": [], "ask": []}}