#!/usr/bin/env python3
"""Fix miscategorized items showing wrong category images"""

import requests

API = "https://oneofstore.com/api"
TOKEN = "changeme-super-secret-admin-api-bearer-moskva17"
HEADERS = {"Authorization": f"Bearer {TOKEN}", "Content-Type": "application/json"}

# Check each category for miscategorized items
categories_to_check = [
    "Tools",
    "Car Audio",
    "Sports",
    "Household Items",
    "Electronics"
]

def main():
    print("Checking categories for miscategorized items...\n")

    for category in categories_to_check:
        print(f"=== {category} ===")
        r = requests.get(f"{API}/categories/{category}/items")
        items = r.json().get('items', [])

        if not items:
            print("  No items\n")
            continue

        print(f"  Total: {len(items)} items")
        print(f"  First item (category image): {items[0]['title'][:60]}\n")

        # Find miscategorized items
        watches_found = []
        jewelry_found = []
        other_misc = []

        for item in items:
            title = item['title'].lower()
            desc = (item.get('description') or '').lower()

            # Check for watches
            if 'watch' in title or 'clock' in title:
                watches_found.append({'id': item['id'], 'title': item['title'][:50]})

            # Check for jewelry
            elif ('bracelet' in title or 'necklace' in title or 'ring' in title or
                  'jewelry' in title or 'earring' in title):
                jewelry_found.append({'id': item['id'], 'title': item['title'][:50]})

            # Check for obvious mismatches
            elif category == "Tools" and ('tent' in title or 'camping' in title):
                other_misc.append({'id': item['id'], 'title': item['title'][:50], 'suggested': 'Sports or Household Items'})
            elif category == "Electronics" and ('tent' in title or 'camping' in title):
                other_misc.append({'id': item['id'], 'title': item['title'][:50], 'suggested': 'Sports or Household Items'})

        # Report and fix
        if watches_found:
            print(f"  ⚠️  Found {len(watches_found)} watches:")
            for w in watches_found:
                print(f"      [{w['id']}] {w['title']}")

            ids = [w['id'] for w in watches_found]
            r = requests.post(f"{API}/admin/items/assign-category",
                             headers=HEADERS,
                             json={"item_ids": ids, "category": "Watches"})
            print(f"  ✅ Moved {r.json().get('updated', 0)} watches to Watches category")

        if jewelry_found:
            print(f"  ⚠️  Found {len(jewelry_found)} jewelry items:")
            for j in jewelry_found:
                print(f"      [{j['id']}] {j['title']}")

            ids = [j['id'] for j in jewelry_found]
            r = requests.post(f"{API}/admin/items/assign-category",
                             headers=HEADERS,
                             json={"item_ids": ids, "category": "Jewelry"})
            print(f"  ✅ Moved {r.json().get('updated', 0)} jewelry items to Jewelry category")

        if other_misc:
            print(f"  ⚠️  Found {len(other_misc)} other miscategorized items:")
            for m in other_misc:
                print(f"      [{m['id']}] {m['title']} → {m['suggested']}")

        if not watches_found and not jewelry_found and not other_misc:
            print("  ✅ No miscategorized items found")

        print()

    print("\n✅ Finished checking categories!")

if __name__ == "__main__":
    main()
