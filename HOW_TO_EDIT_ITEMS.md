# How to Edit Items - Quick Guide

## 🎯 Three Admin Pages Available

You now have **3 admin pages** with a navigation menu:

### 1. **Dashboard** - `/admin`
- Add new products
- Show/hide items
- Delete items
- Manage categories (rename, merge, delete)
- Upload images/videos
- View marketplace integrations

### 2. **Edit Items** - `/admin/items` ⭐ **THIS IS WHAT YOU NEED**
- **Full item editing** - title, price, description, images, etc.
- Search and filter items
- Edit ALL item attributes
- Delete items
- Visual preview of images

### 3. **Categories** - `/admin/categories`
- Bulk assign categories to multiple items
- View category statistics
- Create new categories

---

## ✅ How to Edit an Item

### Step 1: Go to Edit Items Page

Visit: **https://oneof.store/admin/items**

### Step 2: Authenticate

Enter admin token: `changeme-super-secret-admin-api-bearer-moskva17`

Click "Authenticate"

### Step 3: Find Your Item

Use the search and filters:
- **Search:** Type title, SKU, or description
- **Filter by Category:** Select from dropdown
- **Filter by Status:** available, sold, hidden, reserved

### Step 4: Click "Edit" Button

Click the blue "Edit" button on any item

### Step 5: Edit the Item

You can now edit:

#### ✏️ **Title**
Change the item name

#### 💰 **Price**
Update price (supports decimals like 99.99)

#### 📝 **Description**
Multi-line text area for full description

#### 🏷️ **Condition**
Dropdown options:
- New
- Good
- Excellent
- Used
- For Parts
- Refurbished

#### 📂 **Category**
Select from dropdown of all your categories

#### 🎯 **Status**
- Available - Shows in store
- Hidden - Hidden from store
- Sold - Marked as sold
- Reserved - Reserved during checkout

#### 🖼️ **Cover Image URL**
Paste image URL - preview shows immediately

#### 🎨 **Gallery Images**
Multiple images - one URL per line:
```
https://example.com/image1.jpg
https://example.com/image2.jpg
https://example.com/image3.jpg
```

#### 🏷️ **Tags**
Comma-separated tags:
```
facebook, marketplace, vintage, rare
```

### Step 6: Save Changes

Click the green "Save Changes" button

Done! ✅

---

## 📱 Navigation Menu

All admin pages have a menu at the top:

```
[Dashboard] [Edit Items] [Categories]
```

Click any tab to switch between pages.

The active page is highlighted in purple.

---

## 🔍 Search & Filter Features

### Search Box
Type anything - searches:
- Title
- SKU
- Description

### Category Filter
- Dropdown shows all categories with item counts
- Select to show only items in that category

### Status Filter
- Show only: Available, Sold, Hidden, or Reserved items

### Pagination
- 20 items per page
- Previous/Next buttons at bottom
- Shows "X-Y of Z items"

---

## 🎨 Visual Features

### Item Cards
Each item shows:
- **Thumbnail** - Image preview (or "No Image" placeholder)
- **Title** - Item name
- **SKU** - Unique identifier
- **Category** - Gray badge
- **Status** - Color-coded badge:
  - 🟢 Green = Available
  - ⚫ Gray = Sold
  - 🟡 Yellow = Hidden
  - 🔵 Blue = Reserved
- **Price** - In green

### Edit Modal
- Large modal window
- Scrollable content
- Live image preview
- All fields in one place

---

## 🗑️ How to Delete an Item

1. Find the item
2. Click red "Delete" button
3. Confirm in popup
4. Item permanently deleted ⚠️

**Warning:** Deletions are permanent!

---

## 💡 Common Tasks

### Fix Wrong Image
1. Edit item
2. Update "Cover Image URL"
3. Preview shows new image
4. Save

### Change Price
1. Edit item
2. Change "Price" field
3. Save

### Change Category
1. Edit item
2. Select new category from dropdown
3. Save

### Add Multiple Images
1. Edit item
2. In "Gallery Images", paste URLs (one per line)
3. Save
4. Preview shows all images

### Hide Item Temporarily
1. Edit item
2. Change "Status" to "Hidden"
3. Save
4. Item won't show in store (but not deleted)

### Mark as Sold
1. Edit item
2. Change "Status" to "Sold"
3. Save

---

## 🔗 Direct Links

- **Store:** https://oneof.store
- **Admin Dashboard:** https://oneof.store/admin
- **Edit Items:** https://oneof.store/admin/items ⭐
- **Categories:** https://oneof.store/admin/categories

---

## 🔑 Admin Token

**Token:** `changeme-super-secret-admin-api-bearer-moskva17`

**Auto-saves:** Token saved to localStorage (no need to re-enter)

---

## ✨ Features

✅ Full CRUD operations (Create, Read, Update, Delete)
✅ Search across title, SKU, description
✅ Filter by category
✅ Filter by status
✅ Edit ALL item attributes
✅ Live image preview
✅ Gallery support (multiple images)
✅ Tags support
✅ Color-coded status indicators
✅ Pagination
✅ Mobile responsive
✅ Modern, clean UI

---

## 🚀 Quick Start

1. **Go to:** https://oneof.store/admin/items
2. **Login:** Use token `changeme-super-secret-admin-api-bearer-moskva17`
3. **Click "Edit"** on any item
4. **Make changes**
5. **Click "Save Changes"**

That's it! 🎉

---

## 📞 Need Help?

**Documentation:**
- This file - Quick editing guide
- `ADMIN_ITEM_EDITOR_GUIDE.md` - Comprehensive guide
- `CATEGORY_MANAGEMENT_GUIDE.md` - Category management

**Admin Pages:**
- Dashboard: Product management, uploads, integrations
- Edit Items: Full item editing (title, price, images, etc.)
- Categories: Bulk category assignment

---

## 🎯 Summary

You asked: **"How do I edit?"**

**Answer:** Go to https://oneof.store/admin/items

There you can edit:
- ✅ Title
- ✅ Description
- ✅ Price
- ✅ Condition
- ✅ Photos (cover + gallery)
- ✅ Videos (via URLs)
- ✅ Tags
- ✅ Category
- ✅ Status

**Everything!** 🎊
