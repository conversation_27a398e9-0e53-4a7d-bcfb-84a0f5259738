# Category Management Guide - OneOf Store

## What's New? 🎉

Your OneOf Store now has a **beautiful category showcase** instead of random items! Here's what changed:

### 1. Category-First Shopping Experience
- **Beautiful category grid** with images and item counts
- Click categories to filter items
- See total inventory value per category
- Automatic category images from your best items

### 2. Admin Category Management
- Bulk assign items to categories
- Search and filter items
- Create new categories
- See category statistics

### 3. Enhanced API
- Category endpoints with image URLs
- Bulk category assignment
- Better category filtering

---

## How to Use

### For Customers (Public Store)

**Visit:** https://oneofstore.com

1. **Browse by Category** - You'll see a gorgeous category grid at the top
2. **Click a category** - Filter items to show only that category
3. **View items** - See filtered items below
4. **View All** - Click "View All Items" to see everything again

**Features:**
- Categories show:
  - Category name
  - Number of items
  - Total value
  - Beautiful hero image (from first item with image)
- Selected category gets a purple ring + checkmark
- Smooth animations on hover

---

### For Admins (Category Management)

**Visit:** https://oneofstore.com (then navigate to admin panel)

**Or use the API directly:**

#### 1. View All Categories
```bash
curl https://oneofstore.com/api/categories | python3 -m json.tool
```

Returns:
```json
{
  "categories": [
    {
      "name": "Electronics",
      "count": 13,
      "image_url": "https://...",
      "total_value": 688.0
    },
    ...
  ]
}
```

#### 2. Bulk Assign Category to Items
```bash
curl -X POST https://oneofstore.com/api/admin/items/assign-category \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17" \
  -H "Content-Type: application/json" \
  -d '{
    "item_ids": [1, 2, 3, 4],
    "category": "Electronics"
  }'
```

Returns:
```json
{
  "ok": true,
  "updated": 4,
  "not_found": [],
  "items": [
    {
      "id": 1,
      "sku": "FB-123",
      "title": "Laptop",
      "old_category": "Miscellaneous",
      "new_category": "Electronics"
    },
    ...
  ]
}
```

#### 3. Get Items in a Category
```bash
curl https://oneofstore.com/api/categories/Electronics/items | python3 -m json.tool
```

Returns:
```json
{
  "category": "Electronics",
  "count": 13,
  "items": [ ... ]
}
```

#### 4. Create New Category
```bash
curl -X POST https://oneofstore.com/api/categories \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17" \
  -H "Content-Type: application/json" \
  -d '{"name": "Furniture"}'
```

#### 5. Rename Category (affects all items)
```bash
curl -X POST https://oneofstore.com/api/admin/categories/rename \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17" \
  -H "Content-Type: application/json" \
  -d '{
    "old_name": "Miscellaneous",
    "new_name": "Other Items"
  }'
```

#### 6. Merge Categories
```bash
curl -X POST https://oneofstore.com/api/admin/categories/merge \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17" \
  -H "Content-Type: application/json" \
  -d '{
    "source_categories": ["Gadgets", "Tech", "Electronics"],
    "target_category": "Electronics"
  }'
```

---

## Admin Panel (AdminCategories.vue)

A full admin interface is available at: `frontend/src/components/AdminCategories.vue`

**To add it to your site:**

1. **Add route** to your router:
```javascript
// In your router file
import AdminCategories from '@/components/AdminCategories.vue'

const routes = [
  // ... other routes
  {
    path: '/admin/categories',
    name: 'AdminCategories',
    component: AdminCategories
  }
]
```

2. **Access it:**
Visit: https://oneofstore.com/admin/categories

3. **Authenticate:**
Enter admin token: `changeme-super-secret-admin-api-bearer-moskva17`

**Features:**
- ✅ View all categories with stats
- ✅ Search items by title/SKU/description
- ✅ Filter by category
- ✅ Select multiple items (checkbox)
- ✅ Bulk assign category
- ✅ Create new categories
- ✅ Pagination (20 items per page)
- ✅ Real-time updates

---

## Your Current Categories

Based on live data from your API:

| Category | Items | Total Value |
|----------|-------|-------------|
| Miscellaneous | 133 | $9,423 |
| Vehicles | 19 | $1,832 |
| Electronics | 13 | $688 |
| Home Goods | 12 | $435 |
| Musical Instruments | 7 | (value varies) |
| Apparel | 6 | (value varies) |
| Home Improvement Supplies | 5 | (value varies) |
| Sporting Goods | 4 | (value varies) |
| Entertainment & Games | 2 | (value varies) |
| Pet Supplies | 2 | (value varies) |
| Garden & Outdoor | 1 | (value varies) |
| Office Supplies | 1 | (value varies) |

---

## Midjourney Category Images

**See:** `MIDJOURNEY_CATEGORY_PROMPTS.md`

You have custom Midjourney prompts for all 12 categories! Use them to generate beautiful hero images.

**Current behavior:**
- Categories automatically use the first item's image
- This works great and requires no extra setup
- If you want custom hero images, use the Midjourney prompts

**To add custom images:**

Option 1: Store in config file
```javascript
// frontend/src/categoryImages.ts
export const CATEGORY_IMAGES = {
  'Electronics': 'https://yourcdn.com/electronics.webp',
  'Vehicles': 'https://yourcdn.com/vehicles.webp',
  // ...
}
```

Option 2: Add to database
```sql
ALTER TABLE items ADD COLUMN category_image VARCHAR(512);

UPDATE items SET category_image = 'https://...' WHERE category = 'Electronics';
```

---

## Common Tasks

### Clean Up "Miscellaneous"

You have 133 items in Miscellaneous. Here's how to organize them:

1. **Search for specific types:**
```bash
# Find all tools
curl https://oneofstore.com/api/items | grep -i "tool"

# Assign them to "Tools"
curl -X POST https://oneofstore.com/api/admin/items/assign-category \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17" \
  -d '{"item_ids": [1,2,3], "category": "Tools"}'
```

2. **Use the Admin Panel:**
- Filter by "Miscellaneous"
- Search for keywords (e.g., "watch", "tool", "bike")
- Select matching items
- Bulk assign to proper category

### Rename Categories

Make categories more descriptive:
```bash
# "Vehicles" → "Auto Parts & Accessories"
curl -X POST https://oneofstore.com/api/admin/categories/rename \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17" \
  -d '{"old_name": "Vehicles", "new_name": "Auto Parts & Accessories"}'
```

### Consolidate Similar Categories

Merge duplicates or similar categories:
```bash
# Merge "Musical Instruments" + "Audio Equipment" → "Music & Audio"
curl -X POST https://oneofstore.com/api/admin/categories/merge \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17" \
  -d '{
    "source_categories": ["Musical Instruments", "Audio Equipment"],
    "target_category": "Music & Audio"
  }'
```

---

## API Reference

### Public Endpoints (No Auth Required)

- `GET /api/categories` - List all categories with counts and images
- `GET /api/categories/{name}/items` - Get all items in a category

### Admin Endpoints (Require Bearer Token)

- `POST /api/admin/items/assign-category` - Bulk assign category to items
- `POST /api/categories` - Create new category
- `POST /api/admin/categories/rename` - Rename category across all items
- `POST /api/admin/categories/merge` - Merge multiple categories into one
- `DELETE /api/admin/categories/{name}` - Delete category (moves items to "Uncategorized")

---

## Frontend Components

### Store.vue
- **Location:** `frontend/src/components/Store.vue`
- **Changes:** Replaced featured item jumbotron with category showcase
- **Features:**
  - Category grid with images
  - Click to filter
  - Shows item count and total value
  - Responsive design (2/3/4 columns)

### AdminCategories.vue (NEW!)
- **Location:** `frontend/src/components/AdminCategories.vue`
- **Purpose:** Full admin interface for category management
- **Features:**
  - Authentication with admin token
  - Category statistics dashboard
  - Bulk item assignment
  - Search and filter
  - Pagination

---

## Testing

### Test the API
```bash
# Check categories load
curl https://oneofstore.com/api/categories

# Check category filtering works
curl https://oneofstore.com/api/categories/Electronics/items

# Test bulk assignment (with auth)
curl -X POST https://oneofstore.com/api/admin/items/assign-category \
  -H "Authorization: Bearer changeme-super-secret-admin-api-bearer-moskva17" \
  -H "Content-Type: application/json" \
  -d '{"item_ids": [1], "category": "Test Category"}'
```

### Test the Frontend
1. Visit https://oneofstore.com
2. Verify you see "Shop by Category" header
3. Click a category - items should filter
4. Click "View All Items" - should show all items again

---

## Next Steps

1. **Organize Miscellaneous** - Use admin panel to categorize your 133 misc items
2. **Generate Category Images** - Use Midjourney prompts if you want custom images
3. **Add Admin Route** - Add AdminCategories.vue to your router for easy access
4. **Rename Categories** - Make category names more customer-friendly
5. **Marketing** - Categories make your store more professional and browseable!

---

## Deployment Status

✅ **Backend:** Deployed and running on VPS
✅ **Frontend:** Built and deployed to VPS
✅ **API:** All new endpoints working
✅ **Database:** No changes needed (uses existing schema)

**Live URLs:**
- Store: https://oneofstore.com
- Alt domain: https://oneof.store
- API: https://oneofstore.com/api

---

## Support

**Documentation:**
- Category Management: This file
- Midjourney Prompts: `MIDJOURNEY_CATEGORY_PROMPTS.md`
- General Guide: `COMPLETE_STATUS_AND_INSTRUCTIONS.md`
- Quick Reference: `QUICK_REFERENCE.md`

**Admin Token:**
```
changeme-super-secret-admin-api-bearer-moskva17
```

**Example Commands:**
See sections above for all commands with working examples!

---

## Benefits of Category-First Design

✅ **Better UX** - Customers can browse by what they need
✅ **Professional** - Looks like a real store, not a random list
✅ **Filterable** - One-click filtering by category
✅ **Visual** - Beautiful images for each category
✅ **Discoverable** - Customers can explore categories they didn't know you had
✅ **Organized** - You can manage 208+ items easily

---

**Enjoy your new category-powered store!** 🚀
